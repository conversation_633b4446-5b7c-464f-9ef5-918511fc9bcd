import { Attribute, Edge, Graph, Vertex } from '../topology';
import { FormatValidator } from '../utils';
import { ExternalLink } from '../gvis/insights';

import { VisualEditorLogicService } from './visual-editor-logic.service';

describe('VisualEditorLogicService', () => {
  let service: VisualEditorLogicService;

  beforeEach(() => {
    service = new VisualEditorLogicService();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('should be initialized', () => {
    expect(service.schema).toBeDefined();
    expect(service.graph).toBeDefined();
    expect((<any>service).history).toBeDefined();
  });

  it('should set new schema', () => {
    spyOn((<any>service).history, 'clear');
    spyOn((<any>service).history, 'update');

    service.setSchema(<any>{name: '', vertexTypes: [], edgeTypes: []});
    expect(service.schema).toEqual(<any>{name: '', vertexTypes: [], edgeTypes: []});
    expect((<any>service).history.clear).toHaveBeenCalled();
    expect((<any>service).history.update).toHaveBeenCalledWith({ nodes: [], links: [] });
  });

  it('should get edge discriminator map', () => {
    const currGraph = new Graph();
    const multiEdge = new Edge();
    const attr1 = new Attribute();
    attr1.name = 'attr1';
    multiEdge.name = 'multiEdge';
    multiEdge.compositeDiscriminator = ['attr1'];
    multiEdge.attributes.push(attr1);
    currGraph.edgeTypes.push(multiEdge);

    service.setSchema(currGraph);
    const expectedEdgeDiscriminatorMap = {
      'multiEdge': ['attr1']
    };
    expect((<any>service).getEdgeDiscriminatorMap).toEqual(expectedEdgeDiscriminatorMap);
  });

  describe('should get discriminator values in string joined with "#"', () => {
    let currGraph: Graph;
    let edge: Edge;
    let attr1: Attribute;
    let attr2: Attribute;
    let link: ExternalLink;
    beforeEach(() => {
      currGraph = new Graph();
      edge = new Edge();
      attr1 = new Attribute();
      attr2 = new Attribute();
      attr1.name = 'attr1';
      attr2.name = 'attr2';
      edge.name = 'edge';

      link = {
        type: 'eType1',
        source: {
          type: 'vType1',
          id: 'vId1'
        },
        target: {
          type: 'vType1',
          id: 'vId1'
        },
        attrs: {
          attr1: '123',
          attr2: 456
        }
      };
    });

    it('for multi-edge',
      () => {
        edge.discriminatorCount = 2;
        edge.compositeDiscriminator = ['attr1', 'attr2'];
        edge.attributes.push(attr1);
        edge.attributes.push(attr2);
        currGraph.edgeTypes.push(edge);
        spyOn(service, 'getSchemaEdge').and.returnValue(edge);
        expect((<any>service).getDiscriminatorValuesString(link)).toEqual('123#456');
        expect((<any>service).getSchemaEdge).toHaveBeenCalledWith(link.type, true);
      });

    it('for single-edge',
      () => {
        link.type = 'eType1';
        edge.discriminatorCount = 0;
        edge.compositeDiscriminator = [];
        edge.attributes.push(attr1);
        edge.attributes.push(attr2);
        currGraph.edgeTypes.push(edge);
        spyOn(service, 'getSchemaEdge').and.returnValue(edge);

        expect((<any>service).getDiscriminatorValuesString(link)).toEqual('');
        expect((<any>service).getSchemaEdge).toHaveBeenCalledWith(link.type, true);
      });
  });

  it('should return the current graph', () => {
    const value = service.getGraph();
    expect(value.graph).toEqual({ nodes: [], links: [] });
    expect(value.diff).toBe(1);
  });

  describe('should retrieve a vertex from the graph and return its clone', () => {
    it('with an existing vertex',
      () => {
        const v = {
          type: 'type',
          id: 'id'
        };
        service.graph.nodes.push(v);

        const vertex = service.getVertex('type', 'id');
        expect(vertex).toBeDefined();
        expect(vertex).not.toBe(v);
      });

    it('with non-existing vertex',
      () => {
        const vertex = service.getVertex('type', 'id');
        expect(vertex).toBeUndefined();
      });
  });

  describe('should retrieve an edge from the graph and return its clone', () => {
    it('with an existing edge',
      () => {
        // @ts-ignore
        spyOn(service, 'getSchemaEdge').and.returnValue({
          discriminatorCount: 0
        });
        const e = {
          type: 'type',
          source: {
            type: 'type1',
            id: 'id1'
          },
          target: {
            type: 'type2',
            id: 'id2'
          }
        };
        service.graph.links.push(e);

        const edge = service.getEdge(
          'type',
          { type: 'type1', id: 'id1' },
          { type: 'type2', id: 'id2' },
          {}
        );
        expect(edge).toBeDefined();
        expect(edge).not.toBe(e);
      });

    it('with an existing multi edge',
      () => {
        // @ts-ignore
        spyOn(service, 'getSchemaEdge').and.returnValue({
          discriminatorCount: 2,
          compositeDiscriminator: ['a', 'b']
        });
        const e = {
          type: 'type',
          source: {
            type: 'type1',
            id: 'id1'
          },
          target: {
            type: 'type2',
            id: 'id2'
          },
          attrs: {
            a: '123',
            b: 456
          }
        };
        const e2 = {
          type: 'type',
          source: {
            type: 'type1',
            id: 'id1'
          },
          target: {
            type: 'type2',
            id: 'id2'
          },
          attrs: {
            a: '123',
            b: 457
          }
        };
        service.graph.links.push(e);
        service.graph.links.push(e2);

        const edge = service.getEdge(
          'type',
          { type: 'type1', id: 'id1' },
          { type: 'type2', id: 'id2' },
          {
            a: '123',
            b: 456
          }
        );
        expect(edge).toEqual(e);
        expect(edge).not.toBe(e);
      });

    it('with non-existing edge',
      () => {
        let edge = service.getEdge(
          'type',
          { type: 'type1', id: 'id1' },
          { type: 'type2', id: 'id2' },
          {}
        );
        expect(edge).toBeUndefined();

        edge = service.getEdge(
          'type',
          { type: 'type1', id: 'id1' },
          { type: 'type2', id: 'id2' },
          {
            a: '12345',
            b: 456
          }
        );
        expect(edge).toBeUndefined();
      });
  });

  it('should return all vertex types', () => {
    spyOn((<any>service).schema, 'getAllVertexTypes').and.returnValue([]);
    expect(service.getAllVertexTypes()).toEqual([]);
  });

  it('should return all edge types', () => {
    spyOn((<any>service).schema, 'getAllEdgeTypes').and.returnValue([]);
    expect(service.getAllEdgeTypes()).toEqual([]);
  });

  it('should return all edges that match source and target vertices',
    () => {
      // @ts-ignore
      spyOn(service, 'getSchemaEdge').and.callFake((edgeType) => {
        if (edgeType === 'eType1') {
          return {
            fromToVertexTypePairs: [
              {from: 'vType1', to: 'vType1'},
              {from: 'vType2', to: 'vType1'},
              {from: 'vType3', to: 'vType1'},
            ]
          };
        } else if (edgeType === 'eType2') {
          return { fromToVertexTypePairs: [{from: 'vType2', to: 'vType1'}] };
        } else {
          return { fromToVertexTypePairs: [{from: 'vType1', to: 'vType3'}] };
        }
      });
      const allEdgeTypes = ['eType1', 'eType2', 'eType3'];
      expect(service.getMatchEdgeTypesHelper(allEdgeTypes, 'vType2', 'vType1')).toEqual(['eType1', 'eType2']);
      expect(service.getMatchEdgeTypesHelper(allEdgeTypes, 'vType1', 'vType3')).toEqual(['eType3']);
      expect(service.getMatchEdgeTypesHelper(allEdgeTypes, 'vType1', 'vType2')).toEqual([]);
    });

  it('should return the edge types that match the two input vetex types as their two endpoint vertex types',
    () => {
      spyOn((<any>service), 'getMatchEdgeTypesHelper').and.callFake((allEdgeTypes, srcVType, tgtVType) => {
        if (srcVType === 'vType1' && tgtVType === 'vType2') {
          return ['eType1'];
        }
        return [];
      });

      expect(service.getMatchEdgeTypes('vType1', 'vType2')).toEqual(
        {
          matchedEdgeTypes: ['eType1'],
          shouldReverseEdge: false
        }
      );

       expect(service.getMatchEdgeTypes('vType2', 'vType3')).toEqual(
         {
           matchedEdgeTypes: [],
           shouldReverseEdge: false
         }
      );

      expect(service.getMatchEdgeTypes('vType2', 'vType1')).toEqual(
        {
          matchedEdgeTypes: ['eType1'],
          shouldReverseEdge: true
        }
      );
    });

  describe('should retrieve a vertex from the schema and return its clone', () => {
    it('with an existing schema vertex',
      () => {
        const sV = {
          type: 'type',
          clone: () => {}
        };
        spyOn((<any>service).schema, 'getVertex').and.returnValue(sV);
        spyOn(sV, 'clone').and.returnValue(JSON.parse(JSON.stringify(sV)));

        const schemaVertex = <any>service.getSchemaVertex('type');
        expect(schemaVertex).toBeDefined();
        expect(schemaVertex).not.toBe(sV);
      });

    it('with non-existing schema vertex',
      () => {
        spyOn((<any>service).schema, 'getVertex').and.returnValue(undefined);
        const schemaVertex = <any>service.getSchemaVertex('type');
        expect(schemaVertex).toBeUndefined();
      });
  });

  describe('should retrieve an edge from the schema and return its clone', () => {
    it('with an existing schema edge',
      () => {
        const sE = {
          type: 'type',
          clone: () => {}
        };
        spyOn((<any>service).schema, 'getEdge').and.returnValue(sE);
        spyOn(sE, 'clone').and.returnValue(JSON.parse(JSON.stringify(sE)));

        const schemaEdge = <any>service.getSchemaEdge('type');
        expect(schemaEdge).toBeDefined();
        expect(schemaEdge).not.toBe(sE);
      });

    it('with non-existing schema edge',
      () => {
        spyOn((<any>service).schema, 'getEdge').and.returnValue(undefined);
        const schemaEdge = <any>service.getSchemaEdge('type');
        expect(schemaEdge).toBeUndefined();
      });
  });

  describe('should retrieve an edge from the schema using the reverse edge name and return its clone', () => {
    it('with an existing schema edge',
      () => {
        const sE = {
          type: 'type',
          clone: () => {}
        };
        spyOn((<any>service).schema, 'getEdge').and.returnValue(sE);
        spyOn(sE, 'clone').and.returnValue(JSON.parse(JSON.stringify(sE)));

        const schemaEdge = <any>service.getSchemaEdge('type', true);
        expect((<any>service).schema.getEdge).toHaveBeenCalledWith('type', true);
        expect(schemaEdge).toBeDefined();
        expect(schemaEdge).not.toBe(sE);
      });

    it('with non-existing schema edge',
      () => {
        spyOn((<any>service).schema, 'getEdge').and.returnValue(undefined);
        const schemaEdge = <any>service.getSchemaEdge('type', true);
        expect((<any>service).schema.getEdge).toHaveBeenCalledWith('type', true);
        expect(schemaEdge).toBeUndefined();
      });
  });

  it('should support redo', () => {
    const curGraph = {
      nodes: [
        {
          type: 'vType1',
          id: 'vId1'
        },
      ],
      links: [
        {
          type: 'eType1',
          source: {
            type: 'vType1',
            id: 'vId1'
          },
          target: {
            type: 'vType1',
            id: 'vId1'
          }
        }
      ]
    };
    const nextGraph = {
      nodes: [
        {
          type: 'vType2',
          id: 'vId2'
        },
        {
          type: 'vType3',
          id: 'vId3'
        }
      ],
      links: [
        {
          type: 'eType2',
          source: {
            type: 'vType2',
            id: 'vId2'
          },
          target: {
            type: 'vType3',
            id: 'vId3'
          }
        }
      ]
    };
    spyOn((<any>service).history, 'redo').and.returnValue(nextGraph);
    spyOn((<any>service), 'getSchemaEdge').and.returnValue(
      {
        discriminatorCount: 0
      }
    );

    service.graph = curGraph;
    const value = service.redo();
    expect(value.graph).toBe(nextGraph);
    expect(value.diff).toBe(1);
  });

  it('should support undo', () => {
    const curGraph = {
      nodes: [
        {
          type: 'vType1',
          id: 'vId1'
        },
      ],
      links: [
        {
          type: 'eType1',
          source: {
            type: 'vType1',
            id: 'vId1'
          },
          target: {
            type: 'vType1',
            id: 'vId1'
          }
        }
      ]
    };
    const prevGraph = {
      nodes: [
        {
          type: 'vType2',
          id: 'vId2'
        },
        {
          type: 'vType3',
          id: 'vId3'
        }
      ],
      links: [
        {
          type: 'eType2',
          source: {
            type: 'vType2',
            id: 'vId2'
          },
          target: {
            type: 'vType3',
            id: 'vId3'
          }
        }
      ]
    };
    spyOn((<any>service).history, 'undo').and.returnValue(prevGraph);
    spyOn((<any>service), 'getSchemaEdge').and.returnValue(
      {
        discriminatorCount: 0
      }
    );

    service.graph = curGraph;
    const value = service.undo();
    expect(value.graph).toBe(prevGraph);
    expect(value.diff).toBe(1);
  });

  it('should handle NaN in calculating graph difference',
    () => {
      const prevGraph = { nodes: [], links: [] };
      spyOn((<any>service).history, 'undo').and.returnValue(prevGraph);
      spyOn((<any>service), 'getSchemaEdge').and.returnValue(
        {
          discriminatorCount: 0
        }
      );

      const value = service.undo();
      expect(value.graph).toBe(prevGraph);
      expect(value.diff).toBe(0);
    });

  describe('should test if discriminator attributes are equal', () => {
    it('should handle multi edge that have the same discriminator attributes',
      () => {
        spyOn((<any>service), 'getSchemaEdge').and.returnValue(
          {
            discriminatorCount: 2,
            compositeDiscriminator: ['a', 'b']
          }
        );

        expect((<any>service).areDiscriminatorAttributesEqual(
          'test',
          {
            a: '123',
            b: 234,
            c: 2
          },
          {
            a: '123',
            b: 234,
            c: 1
          }
        )).toBeTruthy();
      });

    it('should handle multi edge that have different discriminator attributes',
      () => {
        spyOn((<any>service), 'getSchemaEdge').and.returnValue(
          {
            discriminatorCount: 2,
            compositeDiscriminator: ['a', 'b']
          }
        );

        expect((<any>service).areDiscriminatorAttributesEqual(
          'test',
          {
            a: '113',
            b: 234,
            c: 2
          },
          {
            a: '123',
            b: 234,
            c: 1
          }
        )).toBeFalsy();
      });

    it('should handle single edge',
      () => {
        spyOn((<any>service), 'getSchemaEdge').and.returnValue(
          {
            discriminatorCount: 0,
            compositeDiscriminator: []
          }
        );

        expect((<any>service).areDiscriminatorAttributesEqual(
          'test',
          {
            a: '113',
            b: 234,
            c: 2
          },
          {
            a: '123',
            b: 234,
            c: 1
          }
        )).toBeTruthy();
      });

    it('should handle multi edge that have different discriminator attributes',
      () => {
        spyOn((<any>service), 'getSchemaEdge').and.returnValue(
          {
            discriminatorCount: 2,
            compositeDiscriminator: ['a', 'b']
          }
        );

        expect((<any>service).areDiscriminatorAttributesEqual(
          'test',
          {
            a: '113',
            b: 234,
            c: 2
          },
          {
            a: '123',
            b: 234,
            c: 1
          }
        )).toBeFalsy();
      });
  });

  describe('should add new data to the graph', () => {
    let curGraph: any;

    beforeEach(() => {
      curGraph = {
        nodes: [
          {
            type: 'vType1',
            id: 'vId1',
            attrs: {
              a: '123'
            }
          },
          {
            type: 'vType2',
            id: 'vId2'
          }
        ],
        links: [
          {
            type: 'eType1',
            source: {
              type: 'vType1',
              id: 'vId1'
            },
            target: {
              type: 'vType1',
              id: 'vId1'
            },
            attrs: {
              a: '123'
            }
          },
          {
            type: 'eType3',
            source: {
              type: 'vType1',
              id: 'vId1'
            },
            target: {
              type: 'vType2',
              id: 'vId1'
            },
            attrs: {
              a: '123',
              b: 1,
              c: 1
            }
          }
        ]
      };
    });

    xit('with non-empty data',
      () => {
        const newGraph = {
          nodes: [
            {
              type: 'vType1',
              id: 'vId1',
              attrs: {
                b: '456'
              }
            },
            {
              type: 'vType3',
              id: 'vId3'
            }
          ],
          links: [
            {
              type: 'eType1',
              source: {
                type: 'vType1',
                id: 'vId1'
              },
              target: {
                type: 'vType1',
                id: 'vId1'
              },
              attrs: {
                b: '456'
              }
            },
            {
              type: 'eType2',
              source: {
                type: 'vType1',
                id: 'vId1'
              },
              target: {
                type: 'vType2',
                id: 'vId2'
              }
            },
            {
              type: 'eType3',
              source: {
                type: 'vType1',
                id: 'vId1'
              },
              target: {
                type: 'vType2',
                id: 'vId1'
              },
              attrs: {
                a: '123',
                b: 1,
                c: 2
              }
            },
            {
              type: 'eType3',
              source: {
                type: 'vType1',
                id: 'vId1'
              },
              target: {
                type: 'vType2',
                id: 'vId1'
              },
              attrs: {
                a: '123',
                b: 2,
                c: 1
              }
            },
            {
              type: 'eType3',
              source: {
                type: 'vType1',
                id: 'vId1'
              },
              target: {
                type: 'vType2',
                id: 'vId1'
              },
              attrs: {
                a: '123',
                b: 3,
                c: 1
              }
            },
          ]
        };
        const expectedGraph = {
          nodes: [
            {
              type: 'vType1',
              id: 'vId1',
              attrs: {
                a: '123',
                b: '456'
              }
            },
            {
              type: 'vType2',
              id: 'vId2'
            },
            {
              type: 'vType3',
              id: 'vId3'
            }
          ],
          links: [
            {
              type: 'eType3',
              source: {
                type: 'vType1',
                id: 'vId1'
              },
              target: {
                type: 'vType2',
                id: 'vId1'
              },
              attrs: {
                a: '123',
                b: 3,
                c: 1
              }
            },
            {
              type: 'eType3',
              source: {
                type: 'vType1',
                id: 'vId1'
              },
              target: {
                type: 'vType2',
                id: 'vId1'
              },
              attrs: {
                a: '123',
                b: 2,
                c: 1
              }
            },
            {
              type: 'eType3',
              source: {
                type: 'vType1',
                id: 'vId1'
              },
              target: {
                type: 'vType2',
                id: 'vId1'
              },
              attrs: {
                a: '123',
                b: 1,
                c: 2
              }
            },
            {
              type: 'eType2',
              source: {
                type: 'vType1',
                id: 'vId1'
              },
              target: {
                type: 'vType2',
                id: 'vId2'
              }
            },
            {
              type: 'eType1',
              source: {
                type: 'vType1',
                id: 'vId1'
              },
              target: {
                type: 'vType1',
                id: 'vId1'
              },
              attrs: {
                b: '456'
              }
            }
          ]
        };
        spyOn((<any>service).history, 'update').and.callFake(graph => graph);
        const edge1 = new Edge();
        const edge2 = new Edge();
        const edge3 =  new Edge();
        edge1.name = 'eType3';
        edge1.discriminatorCount = 2;
        edge1.compositeDiscriminator = ['a', 'b'];
        edge2.name = 'eType2';
        edge2.discriminatorCount = 0;
        edge3.name  = 'eType1';
        edge3.discriminatorCount = 0;
        service.schema.edgeTypes = [edge1, edge2, edge3];
        service.graph = curGraph;
        service.addData(newGraph);
        const value = service.getGraph();
        expect(value.graph).toEqual(expectedGraph);
        expect(value.diff).toBe(0.5);
      });

    it('with empty data',
      () => {
        service.graph = curGraph;
        service.addData(undefined);
        const value = service.getGraph();
        expect(value.graph).toEqual(curGraph);
        expect(value.diff).toBe(1);
      });
  });

  it('should clear data', () => {
    const curGraph = {
      nodes: [
        {
          type: 'vType',
          id: 'vId',
        }
      ],
      links: [
        {
          type: 'eType',
          source: {
            type: 'vType',
            id: 'vId'
          },
          target: {
            type: 'vType',
            id: 'vId'
          }
        }
      ]
    };

    service.graph = curGraph;
    service.clearData();
    const value = service.getGraph();
    expect(value.graph).toEqual({ nodes: [], links: [] });
    expect(value.diff).toBe(1);
  });

  describe('should add a vertex to the graph', () => {
    let vertex: any;
    let schemaVertex: any;

    beforeEach(() => {
      vertex = {
        type: 'type',
        id: '123',
        attrs: {
          a: '456'
        }
      };
      schemaVertex = {
        primaryId: {
          name: 'pId',
          type: { name: 'INT' }
        },
        attributes: [
          {
            name: 'a',
            type: { name: 'INT' }
          }
        ]
      };
    });

    it('with success',
      () => {
        const expectedGraph = {
          nodes: [
            {
              type: 'type',
              id: '123',
              attrs: {
                a: '456'
              }
            }
          ],
          links: []
        };
        spyOn(service, 'getSchemaVertex').and.returnValue(schemaVertex);
        spyOn(FormatValidator, 'isInt').and.returnValue({ success: true });
        spyOn((<any>service).history, 'update').and.callFake(graph => graph);

        const result = service.addVertex(vertex);
        expect(result.success).toBeTruthy();
        const value = service.getGraph();
        expect(value.graph).toEqual(expectedGraph);
        expect(value.diff).toBe(1);
      });

    it('without update',
      () => {
        spyOn(service, 'getSchemaVertex').and.returnValue(schemaVertex);
        spyOn(FormatValidator, 'isInt').and.returnValue({ success: true });

        const result = service.addVertex(vertex, false);
        expect(result.success).toBeTruthy();
        const value = service.getGraph();
        expect(value.graph).toEqual({ nodes: [], links: [] });
        expect(value.diff).toBe(1);
      });

    it('with failure',
      () => {
        vertex.id = 'id';
        spyOn(service, 'getSchemaVertex').and.returnValue(schemaVertex);
        spyOn(FormatValidator, 'isInt').and.returnValues({ success: true }, { success: false });

        const result = service.addVertex(vertex);
        expect(result.success).toBeFalsy();
        expect(result.message).toBe(`Attribute "pId"'s value "id" is invalid INT.`);
        const value = service.getGraph();
        expect(value.graph).toEqual({ nodes: [], links: [] });
        expect(value.diff).toBe(1);
      });

    it('with failure when id is empty',
      () => {
        vertex.id = '';
        spyOn(service, 'getSchemaVertex').and.returnValue(schemaVertex);

        const result = service.addVertex(vertex);
        expect(result.success).toBeFalsy();
        expect(result.message).toBe(`Vertex id cannot be empty.`);
        const value = service.getGraph();
        expect(value.graph).toEqual({ nodes: [], links: [] });
        expect(value.diff).toBe(1);
      });
  });

  describe('should add an edge to the graph', () => {
    let edge: any;
    let schemaEdge: any;

    beforeEach(() => {
      edge = {
        type: 'type',
        attrs: {
          a: '456'
        },
        source: {
          type: 'vType1',
          id: 'vId1'
        },
        target: {
          type: 'vType2',
          id: 'vId2'
        }
      };
      schemaEdge = {
        attributes: [
          {
            name: 'a',
            type: { name: 'INT' }
          }
        ]
      };
    });

    it('with success',
      () => {
        const expectedGraph = {
          nodes: [],
          links: [
            {
              type: 'type',
              attrs: {
                a: '456'
              },
              source: {
                type: 'vType1',
                id: 'vId1'
              },
              target: {
                type: 'vType2',
                id: 'vId2'
              }
            }
          ]
        };
        spyOn(service, 'getSchemaEdge').and.returnValue(schemaEdge);
        spyOn(FormatValidator, 'isInt').and.returnValue({ success: true });
        spyOn((<any>service).history, 'update').and.callFake(graph => graph);

        const result = service.addEdge(edge);
        expect(result.success).toBeTruthy();
        const value = service.getGraph();
        expect(value.graph).toEqual(expectedGraph);
        expect(value.diff).toBe(1);
      });

    it('without update',
      () => {
        spyOn(service, 'getSchemaEdge').and.returnValue(schemaEdge);
        spyOn(FormatValidator, 'isInt').and.returnValue({ success: true });

        const result = service.addEdge(edge, false);
        expect(result.success).toBeTruthy();
        const value = service.getGraph();
        expect(value.graph).toEqual({ nodes: [], links: [] });
        expect(value.diff).toBe(1);
      });

    it('with failure',
      () => {
        edge.attrs.a = 'abc';
        spyOn(service, 'getSchemaEdge').and.returnValue(schemaEdge);
        spyOn(FormatValidator, 'isInt').and.returnValues({ success: false });

        const result = service.addEdge(edge);
        expect(result.success).toBeFalsy();
        expect(result.message).toBe(`Attribute "a"'s value "abc" is invalid INT.`);
        const value = service.getGraph();
        expect(value.graph).toEqual({ nodes: [], links: [] });
        expect(value.diff).toBe(1);
      });
  });

  describe('should update a vertex in the graph', () => {
    let vertex: any;
    let schemaVertex: any;
    let curGraph: any;

    beforeEach(() => {
      vertex = {
        type: 'type',
        id: '123',
        attrs: {
          a: '789'
        }
      };
      schemaVertex = {
        primaryId: {
          name: 'pId',
          type: { name: 'INT' }
        },
        attributes: [
          {
            name: 'a',
            type: { name: 'INT' }
          }
        ]
      };
      curGraph = {
        nodes: [
          {
            type: 'type',
            id: '135'
          },
          {
            type: 'type',
            id: '123',
            attrs: {
              a: '456'
            }
          }
        ],
        links: []
      };
    });

    it('with success',
      () => {
        const expectedGraph = {
          nodes: [
            {
              type: 'type',
              id: '135'
            },
            {
              type: 'type',
              id: '123',
              attrs: {
                a: '789'
              }
            }
          ],
          links: []
        };
        spyOn(service, 'getSchemaVertex').and.returnValue(schemaVertex);
        spyOn(FormatValidator, 'isInt').and.returnValue({ success: true });
        spyOn((<any>service).history, 'update').and.callFake(graph => graph);

        service.graph = curGraph;
        const result = service.updateVertex(vertex);
        expect(result.success).toBeTruthy();
        const value = service.getGraph();
        expect(value.graph).toEqual(expectedGraph);
        expect(value.diff).toBe(0);
      });

    it('without update',
      () => {
        spyOn(service, 'getSchemaVertex').and.returnValue(schemaVertex);
        spyOn(FormatValidator, 'isInt').and.returnValue({ success: true });
        spyOn((<any>service).history, 'update').and.callFake(graph => graph);

        service.graph = curGraph;
        const result = service.updateVertex(vertex, false);
        expect(result.success).toBeTruthy();
        const value = service.getGraph();
        expect(value.graph).toEqual(curGraph);
        expect(value.diff).toBe(1);
      });

    it('with failure',
      () => {
        vertex.attrs.a = 'abc';
        spyOn(service, 'getSchemaVertex').and.returnValue(schemaVertex);
        spyOn(FormatValidator, 'isInt').and.returnValue({ success: false });
        spyOn((<any>service).history, 'update').and.callFake(graph => graph);

        service.graph = curGraph;
        const result = service.updateVertex(vertex);
        expect(result.success).toBeFalsy();
        expect(result.message).toBe(`Attribute "a"'s value "abc" is invalid INT.`);
        const value = service.getGraph();
        expect(value.graph).toEqual(curGraph);
        expect(value.diff).toBe(1);
      });
  });

  describe('should update an edge in the graph', () => {
    let edge: any;
    let multiEdge: any;
    let schemaEdge: any;
    let schemaMultiEdge: any;
    let curGraph: any;
    let currGraphMultiEdge: any;

    beforeEach(() => {
      edge = {
        type: 'type2',
        attrs: {
          a: '456'
        },
        source: {
          type: 'vType1',
          id: 'vId1'
        },
        target: {
          type: 'vType2',
          id: 'vId2'
        }
      };
      multiEdge = {
        type: 'type2',
        attrs: {
          a: 455,
          b: '2020-01-01 00:00:00'
        },
        source: {
          type: 'vType1',
          id: 'vId1'
        },
        target: {
          type: 'vType2',
          id: 'vId2'
        }
      };

      schemaEdge = {
        attributes: [
          {
            name: 'a',
            type: { name: 'INT' }
          }
        ]
      };
      schemaMultiEdge = {
        attributes: [
          {
            name: 'a',
            type: { name: 'INT' }
          },
          {
            name: 'b',
            type: { name: 'DATETIME' }
          }
        ],
        discriminatorCount: 1,
        compositeDiscriminator: ['a']
      };
      curGraph = {
        nodes: [],
        links: [
          {
            type: 'type1',
            source: {
              type: 'vType1',
              id: 'vId1'
            },
            target: {
              type: 'vType2',
              id: 'vId2'
            }
          },
          {
            type: 'type2',
            attrs: {
              a: '123'
            },
            source: {
              type: 'vType1',
              id: 'vId1'
            },
            target: {
              type: 'vType2',
              id: 'vId2'
            }
          }
        ]
      };
      currGraphMultiEdge = {
        nodes: [],
        links: [
          {
            type: 'type2',
            attrs: {
              a: 455,
              b: '2020-02-01 00:00:00'
            },
            source: {
              type: 'vType1',
              id: 'vId1'
            },
            target: {
              type: 'vType2',
              id: 'vId2'
            }
          }
        ]
      };
    });

    it('with success on single edge',
      () => {
        const expectedGraph = {
          nodes: [],
          links: [
            {
              type: 'type1',
              source: {
                type: 'vType1',
                id: 'vId1'
              },
              target: {
                type: 'vType2',
                id: 'vId2'
              }
            },
            {
              type: 'type2',
              attrs: {
                a: '456'
              },
              source: {
                type: 'vType1',
                id: 'vId1'
              },
              target: {
                type: 'vType2',
                id: 'vId2'
              }
            }
          ]
        };
        spyOn(service, 'getSchemaEdge').and.returnValue(schemaEdge);
        spyOn(FormatValidator, 'isInt').and.returnValue({ success: true });
        spyOn((<any>service).history, 'update').and.callFake(graph => graph);

        service.graph = curGraph;
        const result = service.updateEdge(edge);
        expect(result.success).toBeTruthy();
        const value = service.getGraph();
        expect(value.graph).toEqual(expectedGraph);
        expect(value.diff).toBe(0);
      });

    it('with success on multi-edge',
      () => {
        const expectedGraph = {
          nodes: [],
          links: [
            {
              type: 'type2',
              attrs: {
                a: 455,
                b: '2020-01-01 00:00:00'
              },
              source: {
                type: 'vType1',
                id: 'vId1'
              },
              target: {
                type: 'vType2',
                id: 'vId2'
              }
            }
          ]
        };
        spyOn(service, 'getSchemaEdge').and.returnValue(schemaMultiEdge);
        spyOn(FormatValidator, 'isInt').and.returnValue({ success: true });
        spyOn((<any>service).history, 'update').and.callFake(graph => graph);

        service.graph = currGraphMultiEdge;
        const result = service.updateEdge(multiEdge);
        expect(result.success).toBeTruthy();
        const value = service.getGraph();
        expect(value.graph).toEqual(expectedGraph);
        expect(value.diff).toBe(0);
      });

    it('without update',
      () => {
        spyOn(service, 'getSchemaEdge').and.returnValue(schemaEdge);
        spyOn(FormatValidator, 'isInt').and.returnValue({ success: true });

        service.graph = curGraph;
        const result = service.updateEdge(edge, false);
        expect(result.success).toBeTruthy();
        const value = service.getGraph();
        expect(value.graph).toEqual(curGraph);
        expect(value.diff).toBe(1);
      });

    it('with failure',
      () => {
        edge.attrs.a = 'abc';
        spyOn(service, 'getSchemaEdge').and.returnValue(schemaEdge);
        spyOn(FormatValidator, 'isInt').and.returnValue({ success: false });

        service.graph = curGraph;
        const result = service.updateEdge(edge);
        expect(result.success).toBeFalsy();
        expect(result.message).toBe(`Attribute "a"'s value "abc" is invalid INT.`);
        const value = service.getGraph();
        expect(value.graph).toEqual(curGraph);
        expect(value.diff).toBe(1);
      });
  });

  describe('should check the validity of the attribute', () => {
    it('with STRING value',
      () => {
        const vertex = {
          type: 'type',
          id: 'abc',
          attrs: {}
        };
        const schemaVertex = {
          primaryId: {
            name: 'pId',
            type: { name: 'STRING' }
          },
          attributes: []
        };
        // @ts-ignore
        spyOn(service, 'getSchemaVertex').and.returnValue(schemaVertex);

        const result = service.addVertex(vertex, false);
        expect(result.success).toBeTruthy();
      });

    it('with INT value',
      () => {
        const vertex = {
          type: 'type',
          id: '123',
          attrs: {}
        };
        const schemaVertex = {
          primaryId: {
            name: 'pId',
            type: { name: 'INT' }
          },
          attributes: []
        };
        // @ts-ignore
        spyOn(service, 'getSchemaVertex').and.returnValue(schemaVertex);
        spyOn(FormatValidator, 'isInt').and.returnValue({ success: true });

        const result = service.addVertex(vertex, false);
        expect(FormatValidator.isInt).toHaveBeenCalledWith('123');
        expect(result.success).toBeTruthy();
      });

    it('with UINT value',
      () => {
        const vertex = {
          type: 'type',
          id: '123',
          attrs: {}
        };
        const schemaVertex = {
          primaryId: {
            name: 'pId',
            type: { name: 'UINT' }
          },
          attributes: []
        };

        // @ts-ignore
        spyOn(service, 'getSchemaVertex').and.returnValue(schemaVertex);
        spyOn(FormatValidator, 'isUInt').and.returnValue({ success: true });

        const result = service.addVertex(vertex);
        expect(FormatValidator.isUInt).toHaveBeenCalledWith('123');
        expect(result.success).toBeTruthy();
      });

    it('with DOUBLE and FLOAT value',
      () => {
        const vertex = {
          type: 'type',
          id: '123.456',
          attrs: {}
        };
        const schemaVertex = {
          primaryId: {
            name: 'pId',
            type: { name: 'DOUBLE' }
          },
          attributes: []
        };
        // @ts-ignore
        spyOn(service, 'getSchemaVertex').and.returnValue(schemaVertex);
        spyOn(FormatValidator, 'isReal').and.returnValue({ success: true });

        const result = service.addVertex(vertex);
        expect(FormatValidator.isReal).toHaveBeenCalledWith('123.456');
        expect(result.success).toBeTruthy();
      });

    it('with BOOL value',
      () => {
        const vertex = {
          type: 'type',
          id: 'true',
          attrs: {}
        };
        const schemaVertex = {
          primaryId: {
            name: 'pId',
            type: { name: 'BOOL' }
          },
          attributes: []
        };
        // @ts-ignore
        spyOn(service, 'getSchemaVertex').and.returnValue(schemaVertex);
        spyOn(FormatValidator, 'isBoolean').and.returnValue({ success: true });

        const result = service.addVertex(vertex);
        expect(FormatValidator.isBoolean).toHaveBeenCalledWith('true');
        expect(result.success).toBeTruthy();
      });

    it('with DATETIME value',
      () => {
        const vertex = {
          type: 'type',
          id: '2017-10-20 10:10:10',
          attrs: {}
        };
        const schemaVertex = {
          primaryId: {
            name: 'pId',
            type: { name: 'DATETIME' }
          },
          attributes: []
        } as Vertex;
        spyOn(service, 'getSchemaVertex').and.returnValue(schemaVertex);
        spyOn(FormatValidator, 'isDatetime').and.returnValue({ success: true });

        const result = service.addVertex(vertex);
        expect(FormatValidator.isDatetime).toHaveBeenCalledWith('2017-10-20 10:10:10');
        expect(result.success).toBeTruthy();
      });
  });

  it('should retain selected vertices and edges',
    () => {
      const curGraph = {
        nodes: [
          {
            type: 'vType1',
            id: 'vId1'
          },
          {
            type: 'vType2',
            id: 'vId2'
          },
          {
            type: 'vType3',
            id: 'vId3'
          }
        ],
        links: [
          {
            type: 'eType1',
            source: {
              type: 'vType3',
              id: 'vId3'
            },
            target: {
              type: 'vType2',
              id: 'vId2'
            }
          },
          {
            type: 'eType2',
            source: {
              type: 'vType1',
              id: 'vId1'
            },
            target: {
              type: 'vType1',
              id: 'vId1'
            }
          },
          {
            type: 'eType3',
            source: {
              type: 'vType1',
              id: 'vId1'
            },
            target: {
              type: 'vType2',
              id: 'vId2'
            }
          }
        ]
      };
      const expectedGraph = {
        nodes: [
          {
            type: 'vType1',
            id: 'vId1'
          },
          {
            type: 'vType3',
            id: 'vId3'
          },
          {
            type: 'vType2',
            id: 'vId2'
          }
        ],
        links: [
          {
            type: 'eType2',
            source: {
              type: 'vType1',
              id: 'vId1'
            },
            target: {
              type: 'vType1',
              id: 'vId1'
            }
          },
          {
            type: 'eType3',
            source: {
              type: 'vType1',
              id: 'vId1'
            },
            target: {
              type: 'vType2',
              id: 'vId2'
            }
          }
        ]
      };
      // @ts-ignore
      spyOn(service, 'getSchemaEdge').and.returnValue({
        discriminatorCount: 0
      });
      spyOn((<any>service).history, 'update').and.callFake(graph => graph);

      service.graph = curGraph;
      service.retain(
        [
          {
            type: 'vType1',
            id: 'vId1'
          },
          {
            type: 'vType3',
            id: 'vId3'
          }
        ],
        [
          {
            type: 'eType2',
            source: {
              type: 'vType1',
              id: 'vId1'
            },
            target: {
              type: 'vType1',
              id: 'vId1'
            },
            attrs: {}
          },
          {
            type: 'eType3',
            source: {
              type: 'vType1',
              id: 'vId1'
            },
            target: {
              type: 'vType2',
              id: 'vId2'
            },
            attrs: {}
          }
        ]
      );
      const value = service.getGraph();
      expect(value.graph).toEqual(expectedGraph);
      expect(value.diff).toBe(0);
    });

  it('should remove selected vertices and edges',
    () => {
      const curGraph = {
        nodes: [
          {
            type: 'vType1',
            id: 'vId1'
          },
          {
            type: 'vType2',
            id: 'vId2'
          },
          {
            type: 'vType3',
            id: 'vId3'
          }
        ],
        links: [
          {
            type: 'eType1',
            source: {
              type: 'vType3',
              id: 'vId3'
            },
            target: {
              type: 'vType2',
              id: 'vId2'
            }
          },
          {
            type: 'eType2',
            source: {
              type: 'vType1',
              id: 'vId1'
            },
            target: {
              type: 'vType1',
              id: 'vId1'
            }
          },
          {
            type: 'eType3',
            source: {
              type: 'vType1',
              id: 'vId1'
            },
            target: {
              type: 'vType2',
              id: 'vId2'
            }
          },
          {
            type: 'eType4',
            source: {
              type: 'vType2',
              id: 'vId2'
            },
            target: {
              type: 'vType1',
              id: 'vId1'
            }
          }
        ]
      };
      const expectedGraph = {
        nodes: [
          {
            type: 'vType2',
            id: 'vId2'
          }
        ],
        links: []
      };
      // @ts-ignore
      spyOn(service, 'getSchemaEdge').and.returnValue({
        discriminatorCount: 0
      });
      spyOn((<any>service).history, 'update').and.callFake(graph => graph);

      service.graph = curGraph;
      service.remove(
        [
          {
            type: 'vType1',
            id: 'vId1'
          },
          {
            type: 'vType3',
            id: 'vId3'
          }
        ],
        [
          {
            type: 'eType3',
            source: {
              type: 'vType1',
              id: 'vId1'
            },
            target: {
              type: 'vType2',
              id: 'vId2'
            }
          }
        ]
      );
      const value = service.getGraph();
      expect(value.graph).toEqual(expectedGraph);
      expect(value.diff).toBe(0);
    });

  it('should clear the graph', () => {
    const curGraph = {
      nodes: [
        {
          type: 'type',
          id: 'id'
        }
      ],
      links: []
    };
    spyOn((<any>service).history, 'update').and.callFake(graph => graph);

    service.graph = curGraph;
    service.removeAll();
    const value = service.getGraph();
    expect(value.graph).toEqual({ nodes: [], links: [] });
    expect(value.diff).toBe(0);
  });
});
