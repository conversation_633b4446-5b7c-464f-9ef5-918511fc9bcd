name: PR Check

on:
  pull_request:
    types: [opened, reopened, ready_for_review, edited, labeled, synchronize]
    branches:
      - main
      - release_*

permissions:
  id-token: write
  contents: write
  pull-requests: write
  checks: write

jobs:
  automerge:
    name: Enable automerge on PR
    runs-on: ubuntu-latest
    if: >-
        github.event.action == 'labeled' && github.event.label.name == 'cicd: auto merge'
    steps:
        - name: Check out code into the source code repository
          uses: actions/checkout@v4
        - name: Enable Pull Request Automerge
          run: gh pr merge --squash --auto ${{ github.event.pull_request.number }}
          env:
            GH_TOKEN: ${{ secrets.GST_BOT_TOKEN }}

  check-dependencies:
    runs-on: ubuntu-latest
    name: Check PR Dependencies
    steps:
    - uses: gregsdennis/dependencies-action@main
      env:
        GITHUB_TOKEN: ${{ secrets.CICD_GITHUB_TOKEN }}

  pre-merge-check:
    name: Check PR
    runs-on: ubuntu-latest
    steps:
    - name: Validate PR
      if: github.event_name == 'pull_request' && github.event.pull_request
      uses: tigergraph/github-actions/pre-merge-check@main
      with:
        github_token: ${{ secrets.GITHUB_TOKEN }}
        jira_user: ${{ vars.JIRA_USERNAME }}
        jira_token: ${{ secrets.JIRA_API_TOKEN }}
        pr_title_regex: ${{ vars.PR_TITLE_REGEX_PATTERN }}

  check-jira-status:
    name: Check JIRA Ticket Status
    runs-on: ubuntu-latest
    steps:
    - name: Check JIRA Ticket Status
      uses: tigergraph/github-actions/check-ticket-status@main
      with:
        github_token: ${{ secrets.GITHUB_TOKEN }}
        jira_user: ${{ vars.JIRA_USERNAME }}
        jira_token: ${{ secrets.JIRA_API_TOKEN }}
        pr_title_regex: ${{ vars.PR_TITLE_REGEX_PATTERN }}