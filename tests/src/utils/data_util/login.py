import logging

from pages.cloud.login.cloud_login_page import CloudLoginPage
from seleniumbase.common import decorators
from utils.data_util.data_resolver import read_test_data


LOGGER = logging.getLogger(__name__)


class LoginUtils():
    test_data = read_test_data(file="tgcloud_test_data.json")
    test_env = test_data.get("test_env")
    default_password = test_data.get("default_password")
    current_email = test_data.get("current_register_account")
    cloud_org = test_data.get("org")
    cloud_username = test_data.get("user")
    cloud_password = test_data.get("password")
    cloud_platform = test_data.get("platform")

    def __init__(self, sb):
        self.sb = sb

    def is_oncloud(self):
        if "tgcloud" in self.test_env:
            return True
        else:
            return False

    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=10)
    def login_cloud(self, org=cloud_org, username=cloud_username, password=cloud_password):
        # login the cloud
        cloudLoginPage = CloudLoginPage(self.sb)
        cloudLoginPage.open_login_page(self.test_env, org, username, password)
        cloudLoginPage.org_login(org, username, password)


    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=10)
    def login_cloud_org_invalid(self, cloud_org, cloud_username, cloud_password):
        # login the cloud
        cloudLoginPage = CloudLoginPage(self.sb)
        cloudLoginPage.open_login_page(self.test_env, cloud_org, cloud_username, cloud_password)
        cloudLoginPage.org_login_invalid(cloud_org, cloud_username, cloud_password)

    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=10)
    def logout_cloud(self):
        # logout the cloud
        cloudLoginPage = CloudLoginPage(self.sb)
        cloudLoginPage.logout()

    @decorators.retry_on_exception(tries=2, delay=1, backoff=2, max_delay=10)
    def login_cloud_other_way(self, login_way, org='', user='', password=''):
        # login the cloud
        cloudLoginPage = CloudLoginPage(self.sb)
        cloudLoginPage.open_login_page(self.test_env, self.cloud_org, self.cloud_username, self.cloud_password)
        if login_way == 'lobby':
            cloudLoginPage.lobby_login(self.cloud_username, self.cloud_password)
        elif login_way == 'okta':
            cloudLoginPage.login_with_okta_sso(org, user, password)
        elif login_way == 'aad':
            cloudLoginPage.login_with_aad_sso(org, user, password)
        elif login_way == 'org':
            cloudLoginPage.org_login(org, user, password)
        else:
            LOGGER.info("Pls specify a login way")

    def register_new_account_invalid(self, username, password, errormsg):
        # login the cloud
        cloudLoginPage = CloudLoginPage(self.sb)
        cloudLoginPage.open_login_page(self.test_env)
        cloudLoginPage.turn_to_singup_page()
        cloudLoginPage.register_action_invalid(username, password, errormsg)


    def register_new_account_cloud(self, is_v4=True):
        # get the mail drop address
        cloudLoginPage = CloudLoginPage(self.sb)
        current_email = cloudLoginPage.get_the_test_email(is_update=is_v4)

        # login cloud portal
        access_env = self.test_env
        if not is_v4:
            access_env = self.return_v3_url()
            cloudLoginPage.open_login_page_v3(access_env)
        else:
          cloudLoginPage.open_login_page(access_env)
        cloudLoginPage.turn_to_singup_page()
        cloudLoginPage.register_action(current_email)

        # verify the invitation in mail box
        cloudLoginPage.wait_the_invitation_email(current_email)
        cloudLoginPage.verify_invitation_email(current_email,is_v4)

        # will grant 50 credits when first login
        if is_v4:
          cloudLoginPage.verify_quicklaunch_workflow()
          cloudLoginPage.waiting_credit_balance_5_mins("50")

        return current_email

    # already register on legacy cloud but first login cloud v4
    def first_login_v4(self, register_email, password=default_password):
        cloudLoginPage = CloudLoginPage(self.sb)
        # clean the session for browser
        cloudLoginPage.access_portal_without_login(self.test_env)
        # accept the terms conditions again in Savanna
        cloudLoginPage.check_terms_conditions()
        cloudLoginPage.fill_survey_savanna()
        cloudLoginPage.verify_quicklaunch_workflow()
        cloudLoginPage.waiting_credit_balance_5_mins("50")

    def reset_password(self, username):
        # login the cloud
        cloudLoginPage = CloudLoginPage(self.sb)
        cloudLoginPage.open_login_page(self.test_env)
        cloudLoginPage.turn_to_forget_password_page()
        cloudLoginPage.reset_password_with_email(username)

    def access_from_v4_to_v3(self):
        # login the cloud
        cloudLoginPage = CloudLoginPage(self.sb)
        cloudLoginPage.open_login_page(self.test_env)
        cloudLoginPage.org_login(self.cloud_org, self.cloud_username, self.cloud_password)
        cloudLoginPage.switch_v4_to_v3()

    def access_from_v3_to_v4(self):
        # login the cloud
        cloudLoginPage = CloudLoginPage(self.sb)
        # use the legacy cloud URL
        test_env = self.return_v3_url()

        cloudLoginPage.open_login_page_v3(test_env)
        cloudLoginPage.org_login_v3(self.cloud_org, self.cloud_username, self.cloud_password)
        cloudLoginPage.switch_v3_to_v4()

    def return_v3_url(self, env=test_env):
         # use the legacy cloud URL
        if "tgcloud.io" in env:
            access_env = "https://classic.tgcloud.io/"
        else:
            access_env = "https://classic.tgcloud-dev.com/"
        return access_env


    def verify_invite_user_join_org(self, org, is_roles_admin, is_exist):
        # get the new test email address
        cloudLoginPage = CloudLoginPage(self.sb)
        if is_exist:
            test_email = self.current_email
        else:
            test_email = cloudLoginPage.get_the_test_email(is_update=False)
        # login the cloud
        self.login_cloud(org)

        #since the mail server is not stable, disable check the email in mail server
        cloudLoginPage.invite_user_join_org(test_email, is_roles_admin)
        return
        if cloudLoginPage.invite_user_join_org(test_email, is_roles_admin):
          # verify on mail drop
          cloudLoginPage.wait_the_invitation_email(test_email)
          cloudLoginPage.verify_accept_invitation_email(test_email, is_exist)

          # check the login user no admin permission
          assert cloudLoginPage.is_the_admin_page_exists() == is_roles_admin






