"""
Access or manipulate elements of the TGCloud Login page
"""
import logging

import allure
from base.cloud_basepage import CloudBasePage
from common import common_settings
from locators.cloud.cloud_locators import (CloudHomeLocators, LogInLocators, LogOutLocators)
from seleniumbase import decorators
from datetime import datetime
from locators.cloud.cloud_locators import BillingLocators, CloudNavigationLocators
from locators.cloud.workgroup_locators import WorkgroupLocators
from locators.cloud.cloud_locators import CloudInviteUsersLocators
from utils.data_util import update_test_data
from utils.data_util.data_resolver import read_test_data


LOGGER = logging.getLogger(__name__)


test_data = read_test_data(file="tgcloud_test_data.json")
current_register_account = test_data.get("current_register_account")
# get current time
now = datetime.now()
formatted_current_time = now.strftime('%Y%m%d%H%M%S')
default_password = test_data.get("default_password")
tgcloud_test_data_file = "tgcloud_test_data.json"

class CloudLoginPage(CloudBasePage):
    @allure.step("Opening login page")
    def open_login_page(self, tgc_env, cloud_org="", cloud_username="", cloud_password=""):
        LOGGER.info("login URL: " + tgc_env)
        LOGGER.info("login org: " + cloud_org + ", login_username:" + cloud_username + ", login_password:" + cloud_password)
        self.sb.maximize_window()
        self.sb.open(tgc_env)
        self.sb.wait_for_ready_state_complete()
        self.sb.wait_for_element_present(LogInLocators.login_with_org_btn, timeout=common_settings.WAIT_RENDER_TIME_OUT)
        # self.sb.assert_title(LogInLocators.login_title)

    def open_login_page_v3(self, tgc_env, cloud_org="", cloud_username="", cloud_password=""):
        LOGGER.info("login URL: " + tgc_env)
        LOGGER.info("login org: " + cloud_org + ", login_username:" + cloud_username + ", login_password:" + cloud_password)
        self.sb.maximize_window()
        self.sb.open(tgc_env)
        self.sb.wait_for_ready_state_complete()
        self.sb.wait_for_element_present(LogInLocators.login_with_org_btn, timeout=common_settings.WAIT_RENDER_TIME_OUT)

    def access_portal_without_login(self, tgc_env):
        LOGGER.info("login URL: " + tgc_env)
        self.sb.maximize_window()
        self.sb.open(tgc_env)
        self.sb.wait_for_ready_state_complete()
        #self.sb.wait_for_element_present(CloudHomeLocators.tgcloud_icon, timeout=common_settings.WAIT_RENDER_TIME_OUT)

    # open temp mail server to get the temp email address
    def open_mail_page(self, address):
        email_url = "https://maildrop.cc/inbox/{}".format(address)
        LOGGER.info("Mail URL: " + email_url)

        self.sb.maximize_window()
        self.sb.open(email_url)
        self.sb.wait_for_ready_state_complete()
        self.sb.attach_allure_screenshot(f"Login mailbox screenshot")
        self.sb.wait_for_element_present(LogInLocators.view_mailbox, timeout=common_settings.WAIT_RENDER_TIME_OUT)

        if address == "" :
            self.sb.slow_click(LogInLocators.view_mailbox)
            # retry get the mail address if not auto generate
            max_attempts = 5
            attempts = 0
            while attempts < max_attempts:
                # will exit if the mail is generate
                auto_mail_address = self.sb.find_elements(LogInLocators.email_value)[0].get_attribute("value")
                if auto_mail_address != "":
                    break
                self.sb.slow_click(LogInLocators.view_mailbox)
                LOGGER.info(f"Clicked refresh generate mailbox address button, attempt {attempts + 1}")
                attempts += 1
                if attempts == max_attempts:
                    LOGGER.info("Still not auto generate email address after retry 5 times.")

        # wait the loading process render if the first login
        self.sb.wait_for_element_present(LogInLocators.mail_in_loading, timeout=common_settings.WAIT_RENDER_TIME_OUT)
        self.sb.wait_for_element_absent(LogInLocators.mail_in_loading, timeout=common_settings.WAIT_RENDER_TIME_OUT)

    # open mail drop to get the test account
    def get_the_test_email(self, is_update=False):
        # first login with empty email address
        self.open_mail_page("")

        email_address = self.sb.find_elements(LogInLocators.email_value)[0].get_attribute("value")
        default_email = "{}@maildrop.cc".format(email_address)
        LOGGER.info("Current Mail Drop Address: " + default_email)

        # sleep 5s to waiting the test mail active
        self.sb.sleep(5)
        # update the current register account to tgcloud_test_data.json
        if is_update:
            update_test_data.update_test_data_base_on_params("", "", tgcloud_test_data_file, default_email)
        return default_email

    def wait_the_invitation_email(self, mail_address):
        # second login with specific email address
        self.open_mail_page("?mailbox=" + mail_address.split('@')[0])
        if self.sb.is_element_present(LogInLocators.refresh_mail_button_first):
            max_attempts = 30
            attempts = 0
            while attempts < max_attempts:
                # will exit the refersh if have one email
                if self.sb.is_element_present(LogInLocators.refresh_mail_button_second):
                    break
                self.sb.click(LogInLocators.refresh_mail_button_first)
                LOGGER.info(f"Clicked refresh mailbox button, attempt {attempts + 1}")
                self.sb.sleep(5)
                attempts += 1
                if attempts == max_attempts:
                    LOGGER.info("Still not receive the invation email in inbox after waiting 150s.")

    def verify_invitation_email(self, email, is_v4):
        LOGGER.info("Start to verify cloud invatation link.")
        self.sb.attach_allure_screenshot(f"The mailbox screenshot")
        self.sb.wait_for_element_present(LogInLocators.refresh_mail_button_second, timeout=common_settings.WAIT_RENDER_TIME_OUT)
        # the mail content in iframe
        self.sb.switch_to_frame(frame="iframe")
        self.open_url(LogInLocators.direct_email_verify_url)
        self.sb.attach_allure_screenshot(f"Redirect to login page screenshot")
        try:
          self.sb.wait_for_element_present(LogInLocators.login_with_org_btn, timeout=common_settings.WAIT_CLICK_TIME_OUT)
          # login with username and password
          self.lobby_login(email, default_password, is_check=False)
        except Exception as e:
          LOGGER.info("No need to login again when register a new account")

        #accept the terms conditions
        self.check_terms_conditions()
        #fill in survey if register from v4
        if is_v4:
            self.fill_survey_savanna()

    def verify_accept_invitation_email(self, email, is_exist=False):
        LOGGER.info("Start to verify cloud invatation link for invite the exists user.")
        self.sb.attach_allure_screenshot(f"The mailbox screenshot")
        self.sb.wait_for_element_present(LogInLocators.refresh_mail_button_second, timeout=common_settings.WAIT_RENDER_TIME_OUT)
        # the mail content in iframe
        self.sb.switch_to_frame(frame="iframe")
        self.open_url(LogInLocators.direct_email_accept_url)
        self.sb.attach_allure_screenshot(f"Redirect to login page screenshot")
        self.sb.wait_for_element_present(LogInLocators.accept_invation_tips, timeout=common_settings.WAIT_RENDER_TIME_OUT)

        if is_exist:
            LOGGER.info("The user is already exists, will turn to login page")
            self.waiting_click_button(LogInLocators.log_in_link)
            # click again if exist to more stable
            self.click_button_if_present(LogInLocators.log_in_link)
        # login with username and password
        self.accept_invation_login(email, default_password, is_exist)
        LOGGER.info("Successfully invite the new accountto test org")
        self.sb.attach_allure_screenshot(f"Accept the invitation and login to cloud portal screenshot")

    # access the sign up a new account in cloud portal
    def turn_to_singup_page(self):
        self.sb.wait_for_element_present(LogInLocators.sign_up_link, timeout=common_settings.ASSERT_TIMEOUT)
        self.sb.click(LogInLocators.sign_up_link)
        self.sb.wait_for_element_present(LogInLocators.log_in_link, timeout=common_settings.ASSERT_TIMEOUT)

    # access the reset password in cloud portal
    def turn_to_forget_password_page(self):
        self.sb.wait_for_element_present(LogInLocators.forget_password, timeout=common_settings.ASSERT_TIMEOUT)
        self.sb.click(LogInLocators.forget_password)
        self.sb.wait_for_element_present(LogInLocators.tips_reset_password, timeout=common_settings.ASSERT_TIMEOUT)

    # access the admin managemant page in cloud portal
    def turn_to_under_admin_page(self, page_title):
        # turn to admin management page
        self.sb.wait_for_element_present(CloudNavigationLocators.Admin_span, timeout=common_settings.ASSERT_TIMEOUT)
        if not self.sb.is_element_present(CloudNavigationLocators.under_admin_manage_span.format(page_title)):
            self.sb.click(CloudNavigationLocators.Admin_span)
            self.sb.wait_for_element_clickable(CloudNavigationLocators.under_admin_manage_span.format(page_title), timeout=common_settings.WAIT_RENDER_TIME_OUT)
            self.sb.click(CloudNavigationLocators.under_admin_manage_span.format(page_title))

    # check the admin management mune is exists
    def is_the_admin_page_exists(self):
        # return the admin management page status
        return self.sb.is_element_present(CloudNavigationLocators.Admin_span)

    def check_terms_conditions(self):
         #accept the terms
        self.sb.wait_for_element_present(LogInLocators.accept_button, timeout=common_settings.WAIT_CREATE_WS_TIME_OUT)
        self.sb.click(LogInLocators.accept_button)
        self.sb.attach_allure_screenshot(f"Accept the terms screenshot")
        self.sb.wait_for_ready_state_complete()

        #check the accept panel disappear
        self.sb.wait_for_element_not_visible(LogInLocators.decline_button, timeout=common_settings.WAIT_RENDER_TIME_OUT)
        LOGGER.info("Successfully register the new account and login to cloud portal")

    # verify the quicklaunch workflow and terminate resource to saving cost
    def verify_quicklaunch_workflow(self):
        self.check_quicklaunch_workspace()
        from pages.cloud.workgroup.workgroup_home_page import WorkgroupHomePage
        whp = WorkgroupHomePage(self.sb)
        workgroup_name = "MyWorkgroup"
        workspace_name = "MyWorkspace"
        database_name = "MyDatabase"
        # check the workspace ready and clean it
        whp.check_workspace_is_ready(workgroup_name)
        whp.check_graph_admin()
        whp.terminate_RW_workspace(workgroup_name=workgroup_name)
        self.sb.wait_for_element_not_visible(WorkgroupLocators.text_button.format(workspace_name), timeout=common_settings.WAIT_CREATE_WS_TIME_OUT)

        whp.terminate_workspace_DB(workgroup_name=workgroup_name)
        self.sb.wait_for_element_not_visible(WorkgroupLocators.text_button.format(database_name), timeout=common_settings.WAIT_CREATE_WS_TIME_OUT)

        whp.terminate_workgroup(rw_workgroup_name=workgroup_name)

    # check the quick launch workspace will provisioning when success register a new account
    def check_quicklaunch_workspace(self):
        # need to re-accept the terms if user try to access Savanna from v3
        if self.sb.is_element_present(LogInLocators.accept_button):
          self.check_terms_conditions()

        LOGGER.info("Check the quick launch workspace is in progress ... ")
        self.sb.wait_for_element_present(LogInLocators.setup_workspace, timeout=common_settings.WAIT_RENDER_TIME_OUT)
        self.sb.attach_allure_screenshot(f"quick launch workspace setup")
        self.sb.wait_for_element_absent(LogInLocators.setup_workspace, timeout=common_settings.WAIT_RESUME_WS_TIME_OUT)
        if self.sb.is_element_present(LogInLocators.explore_workspace):
          self.sb.wait_for_element_present(LogInLocators.explore_workspace, timeout=common_settings.WAIT_RENDER_TIME_OUT)
          self.sb.click(LogInLocators.explore_workspace)
          self.sb.wait_for_element_absent(LogInLocators.explore_workspace, timeout=common_settings.WAIT_RENDER_TIME_OUT)

    def waiting_credit_balance_5_mins(self, credits):
        # need to re-accept the terms if user try to access Savanna from v3
        if self.sb.is_element_present(LogInLocators.accept_button):
          self.check_terms_conditions()

        max_attempts = 60
        attempts = 0
        LOGGER.info("Check the default free credits($ {}) will granted.".format(credits))

        # # there is an issue: https://graphsql.atlassian.net/browse/TCE-5983
        while attempts < max_attempts:
            # will exit the refersh if the current credit is same as input
            self.turn_to_under_admin_page("Billing")
            credits_value = self.sb.get_text(BillingLocators.remarning_credit_balance_value)
            # Remove the dollar sign and whitespace, then convert to float
            numeric_value = float(credits_value.replace('$', '').replace(' ', ''))
            LOGGER.info(f"the current free credits is {numeric_value}")
            if numeric_value >= float(credits):
                break
            LOGGER.info(f"refresh the billing management, attempt {attempts + 1}")
            self.sb.refresh()
            self.sb.sleep(10)
            attempts += 1
            if attempts == max_attempts:
                self.sb.attach_allure_screenshot(f"current credit info for final")
                self.sb.assert_true(False, "Still not grant the free credits after waiting 300s.")

        self.sb.attach_allure_screenshot(f"current credit info for register")


    def invite_user_join_org(self, user, roles_admin=False):
        LOGGER.info("Start invite the user of {} join the test org.".format(user))
        self.turn_to_under_admin_page("Users")
        self.sb.attach_allure_screenshot(f"open the Users management page successfully")

        self.waiting_click_button(CloudInviteUsersLocators.invite_users)
        self.sb.wait_for_element_present(CloudInviteUsersLocators.add_email_box, timeout=common_settings.WAIT_RENDER_TIME_OUT)
        self.sb.type(CloudInviteUsersLocators.add_email_box, user)

        # will return is the user exists
        if self.sb.is_element_present(CloudInviteUsersLocators.user_exists):
            LOGGER.info("The user of {} is already exists the test org.".format(user))
            self.sb.attach_allure_screenshot("Since the user is exists, skip invite the user {}".format(user))
            return False

        self.waiting_click_button(CloudInviteUsersLocators.add_button)
        self.sb.attach_allure_screenshot("Add the new user {} wait to invite".format(user))

        # will choose the org member as by default
        if roles_admin:
            self.sb.wait_for_element_present(CloudInviteUsersLocators.org_admin, timeout=common_settings.WAIT_RENDER_TIME_OUT)
            self.sb.slow_click(CloudInviteUsersLocators.org_admin)

        self.waiting_click_button(CloudInviteUsersLocators.invite_button)
        self.sb.wait_for_element_not_visible(CloudInviteUsersLocators.cancel_button, timeout=common_settings.WAIT_RENDER_TIME_OUT)
        self.sb.attach_allure_screenshot("Finish invite the new user {}".format(user))
        LOGGER.info("Accept request for invite the user of {} join the test org.".format(user))
        return True


    # wait element present and the slow click
    def waiting_click_button(self, button_element):
        self.sb.wait_for_element_present(button_element, timeout=common_settings.WAIT_RENDER_TIME_OUT)
        self.sb.wait_for_element_clickable(button_element, timeout=common_settings.WAIT_RENDER_TIME_OUT)
        self.sb.slow_click(button_element)
        self.sb.wait_for_ready_state_complete()

    # if element present and click it
    def click_button_if_present(self, button_element):
        if self.sb.is_element_present(button_element):
          self.sb.click(button_element)

    # wait element present and the hover then click
    def open_url(self, link_element):
        self.sb.wait_for_element_present(link_element, timeout=common_settings.WAIT_RENDER_TIME_OUT)
        verify_link = self.sb.get_text(link_element, timeout=common_settings.WAIT_RENDER_TIME_OUT)
        LOGGER.info("open the verification link: {}".format(verify_link))
        self.sb.maximize_window()
        self.sb.open(verify_link)
        self.sb.wait_for_ready_state_complete()

    @allure.step("Reset the password with email")
    def reset_password_with_email(self, user):
        # Click "Login with organization" button
        if user == "exists_user":
            user = test_data["current_register_account"]
        self.sb.type(LogInLocators.email_input, user)
        self.sb.click(LogInLocators.lobby_continue_btn)

        LOGGER.info("the reset password link will send to u email")
        self.sb.wait_for_element_visible(LogInLocators.check_your_email, timeout=common_settings.WAIT_RENDER_TIME_OUT)

    # fill the sruvey when register new account on savanna
    def fill_survey_savanna(self):
        self.sb.wait_for_element_visible(LogInLocators.survey_title, timeout=common_settings.WAIT_RENDER_TIME_OUT)
        if self.sb.is_element_present(LogInLocators.survey_title):
            # fill in the columns
            self.sb.type(LogInLocators.survey_firstname, "QE")
            self.sb.type(LogInLocators.survey_lastname, "Tigergraph")
            self.sb.type(LogInLocators.survey_company, "CloudQE")
            # choose the role and how to use
            self.sb.slow_click(LogInLocators.survey_role)
            if self.sb.is_element_present(LogInLocators.role_business):
                self.sb.slow_click(LogInLocators.role_business)
            self.sb.slow_click(LogInLocators.survey_how)
            if self.sb.is_element_present(LogInLocators.how_test):
                self.sb.slow_click(LogInLocators.how_test)
            self.sb.slow_click(LogInLocators.input_antiMoney)

            # submit the survey
            self.sb.click(LogInLocators.survey_submit)
            # wait set up the quick launch workspace
            self.sb.wait_for_element_visible(LogInLocators.setup_workspace, timeout=common_settings.WAIT_RENDER_TIME_OUT)

    @allure.step("Login with username/password successfully")
    def org_login(self, org, user, password):
        # Click "Login with organization" button
        self.input_org_login(org)
        self.sb.type(LogInLocators.user_input, user)
        self.sb.type(LogInLocators.password_input, password)
        self.sb.click(LogInLocators.continue_login_btn)
        self.sb.wait_for_element_not_visible(LogInLocators.password_input, timeout=common_settings.WAIT_CLICK_TIME_OUT)
        LOGGER.info("input_password not visible")
        self.sb.wait_for_element_visible(CloudHomeLocators.tgcloud_icon, timeout=common_settings.WAIT_CLICK_TIME_OUT)
        self.close_tips_panel()
        self.sb.attach_allure_screenshot(f"Login to {org} with user {user} successfully")

    @allure.step("Login with username/password on legacy cloud successfully")
    def org_login_v3(self, org, user, password):
        # Click "Login with organization" button
        self.input_org_login(org)
        self.sb.type(LogInLocators.user_input, user)
        self.sb.type(LogInLocators.password_input, password)
        self.sb.click(LogInLocators.continue_login_btn)
        self.sb.wait_for_element_not_visible(LogInLocators.password_input, timeout=common_settings.WAIT_CLICK_TIME_OUT)
        LOGGER.info("input_password not visible")
        self.sb.wait_for_element_visible(CloudHomeLocators.my_cluster, timeout=common_settings.WAIT_CLICK_TIME_OUT)
        self.sb.attach_allure_screenshot(f"Login to {org} with user {user} successfully on cloud V3")

    @allure.step("Login with org name")
    def input_org_login(self, org):
        # Click "Login with organization" button
        self.sb.click_if_visible(LogInLocators.login_with_org_btn, timeout=common_settings.WAIT_RENDER_TIME_OUT)
        self.sb.wait_for_element_present(LogInLocators.org_title_h1, timeout=common_settings.WAIT_CLICK_TIME_OUT)
        self.sb.type(LogInLocators.org_input, org)
        self.sb.click(LogInLocators.continue_btn)

    @allure.step("Login with username/password with invalid conditions")
    def org_login_invalid(self, org, user, password):
        # Click "Login with organization" button
        self.input_org_login(org)
        if org == "not_exists_org_check":
            if self.sb.is_element_present(LogInLocators.org_name_invalid):
                # check will popup error to indicate the org name is not exists
                self.sb.assert_element_present(LogInLocators.org_name_invalid)
                return
        self.sb.type(LogInLocators.user_input, user)
        self.sb.type(LogInLocators.password_input, password)
        self.sb.click(LogInLocators.continue_login_btn)
        LOGGER.info("input_password is visible in error condition")
        self.sb.wait_for_element_visible(LogInLocators.password_input, timeout=common_settings.WAIT_CLICK_TIME_OUT)
        if self.sb.is_element_present(LogInLocators.user_password_invalid):
            # check will popup error to indicate the username or email is invalid
            self.sb.assert_element_present(LogInLocators.user_password_invalid)

    def close_tips_panel(self):
        try:
            LOGGER.info("Start close tips panel if visible")
            if self.sb.is_element_present(CloudHomeLocators.tips_button):
                LOGGER.info("closed tips panel successfully")
                self.sb.click(CloudHomeLocators.tips_button)
            if self.sb.is_element_present(CloudHomeLocators.guide_button):
                LOGGER.info("closed guide panel successfully")
                self.sb.click(CloudHomeLocators.guide_button)
        except Exception as e:
            LOGGER.info("close tips panel failed: " + str(e))
            self.sb.attach_allure_screenshot("close tips panel failed")

    def lobby_login(self, user, password, is_check=True):
        LOGGER.info(f"Start login TGCloud using user {user}/password {password}")
        self.sb.type(LogInLocators.user_input, user)
        self.sb.type(LogInLocators.password_input, password)
        self.sb.click(LogInLocators.lobby_continue_btn)

        if is_check:
          self.sb.wait_for_element_visible(CloudHomeLocators.tgcloud_icon, timeout=common_settings.WAIT_RENDER_TIME_OUT)
          self.sb.attach_allure_screenshot(f"Login TGCLoud with user {user} successfully")
          self.check_account_info(user)

    def accept_invation_login(self, user, password, is_exist=True):
        LOGGER.info(f"Start accept invation login TGCloud using user {user}/password {password}")
        if is_exist:
          LOGGER.info("the exists user, waiting the login page render")
          self.sb.wait_for_element_present(LogInLocators.login_with_org_btn, timeout=common_settings.WAIT_RENDER_TIME_OUT)
        self.sb.wait_for_element_present(LogInLocators.lobby_continue_btn, timeout=common_settings.WAIT_RENDER_TIME_OUT)
        self.sb.type(LogInLocators.password_input, password)
        try:
          self.sb.click(LogInLocators.lobby_continue_btn)
          self.sb.wait_for_element_visible(CloudHomeLocators.tgcloud_icon, timeout=common_settings.WAIT_RENDER_TIME_OUT)
        except Exception as e:
          LOGGER.info("second time to click the login button")
          self.click_button_if_present(LogInLocators.lobby_continue_btn)
          self.sb.wait_for_element_visible(CloudHomeLocators.tgcloud_icon, timeout=common_settings.WAIT_RENDER_TIME_OUT)
        self.sb.attach_allure_screenshot(f"Login TGCLoud with user {user} successfully")

        if not is_exist:
            #accept the terms
            self.sb.wait_for_element_present(LogInLocators.accept_button, timeout=common_settings.WAIT_RENDER_TIME_OUT)
            self.sb.click(LogInLocators.accept_button)
            self.sb.attach_allure_screenshot(f"Accept the terms screenshot")
            self.sb.wait_for_ready_state_complete()
            #check the accept terms disappear after login
            self.sb.wait_for_element_not_visible(LogInLocators.decline_button, timeout=common_settings.WAIT_RENDER_TIME_OUT)
        # check the current login user
        self.check_account_info(user)

    def register_action(self, user, password=default_password):
        LOGGER.info(f"Start registration TGCloud using user {user}/password {password}")
        self.sb.type(LogInLocators.email_input, user)
        self.sb.type(LogInLocators.password_input, password)
        self.sb.click(LogInLocators.lobby_continue_btn)

        # there exists an issue: https://graphsql.atlassian.net/browse/TCE-4217
        #self.sb.wait_for_element_present(LogInLocators.verify_email, timeout=common_settings.WAIT_RENDER_TIME_OUT)
        self.sb.sleep(5)
        self.sb.attach_allure_screenshot(f"Accept registration TGCLoud with user {user} successfully")

    @allure.step("Register with username/password with invalid conditions")
    def register_action_invalid(self, user, password, errormsg):
        # Click "Login with organization" button
        self.sb.type(LogInLocators.email_input, user)
        self.sb.type(LogInLocators.password_input, password)
        self.sb.click(LogInLocators.lobby_continue_btn)

        LOGGER.info("the warning is visible in register error condition")
        self.sb.wait_for_element_visible(LogInLocators.register_error_text.format(errormsg), timeout=common_settings.WAIT_RENDER_TIME_OUT)

        # TO-DO need to verify reset password with whole process

    @allure.step("Login org using okta sso")
    def login_with_okta_sso(self, org, user, password):
        """
        Description: Workflow of logging in using okta sso
        """
        LOGGER.info(f"Start login org {org} with user {user} ")
        # click "Login with organization"
        self.sb.click_if_visible(LogInLocators.login_with_org_btn, timeout=common_settings.WAIT_RENDER_TIME_OUT)
        self.sb.wait_for_element_present(LogInLocators.org_title_h1, timeout=common_settings.WAIT_CLICK_TIME_OUT)
        # type org_name
        self.sb.type(LogInLocators.org_input, org)
        self.sb.click(LogInLocators.continue_btn)
        # click "Continue with Okta"
        self.sb.wait_for_element_visible(LogInLocators.continue_okta_btn, timeout=common_settings.WAIT_CLICK_TIME_OUT)
        self.sb.click(LogInLocators.continue_okta_btn)
        # Wait for redirecting to okta login page
        self.sb.wait_for_element_present(LogInLocators.okta_signin_main, timeout=common_settings.WAIT_CLICK_TIME_OUT)
        self.sb.type(LogInLocators.okta_login_username_input, user)
        self.sb.type(LogInLocators.okta_login_password_input, password)
        self.sb.attach_allure_screenshot("LoginOktaSSO")
        # click "LogIn" to login okta and redirect back to tgcloud
        self.sb.click(LogInLocators.okta_login_submit)
        LOGGER.info("assert login successfully, wait_for_element_visible")
        self.wait_okta_login_circle_progressbar()
        self.sb.wait_for_element_visible(CloudHomeLocators.tgcloud_icon, timeout=common_settings.WAIT_CREATE_WS_TIME_OUT)
        # self.sb.wait_for_element_present(CloudHomeLocators.org_name_div.format(org), timeout=common_settings.WAIT_CLICK_TIME_OUT)
        self.sb.wait_for_element_clickable(CloudHomeLocators.account_icon, timeout=common_settings.WAIT_CLICK_TIME_OUT)
        self.sb.click(CloudHomeLocators.account_icon, timeout=common_settings.WAIT_CLICK_TIME_OUT)
        self.sb.wait_for_element_clickable(CloudHomeLocators.common_span_button.format("Switch Organization"), timeout=common_settings.WAIT_CLICK_TIME_OUT)
        self.sb.click(CloudHomeLocators.common_span_button.format("Switch Organization"))
        self.sb.assert_text(org, timeout=common_settings.WAIT_CLICK_TIME_OUT)
        self.sb.attach_allure_screenshot(f"Login to {org} with Okta user {user} successfully")

    def wait_okta_login_circle_progressbar(self, visible_timeout=common_settings.WAIT_CLICK_TIME_OUT, not_visible_timeout=common_settings.WAIT_RENDER_TIME_OUT):
      try:
        self.sb.wait_for_element_visible(CloudHomeLocators.okta_login_circle_progressbar, timeout=visible_timeout)
        LOGGER.info("WS_operate_circle_progressbar visible")
        self.sb.wait_for_element_not_visible(CloudHomeLocators.okta_login_circle_progressbar, timeout=not_visible_timeout)
        LOGGER.info("WS_operate_circle_progressbar NOT visible")
      except Exception as e:
        LOGGER.info("wait_WS_operate_circle_progressbar exception:" + str(e))
        self.sb.attach_allure_screenshot("wait_WS_operate_circle_progressbar exception")

    @allure.step("Login org using aad sso")
    def login_with_aad_sso(self, org, user, password):
        """
        Description: Workflow of logging in using aad sso
        """
        LOGGER.info(f"Start login org {org} with user {user} ")
        # click "Login with organization"
        self.sb.click_if_visible(LogInLocators.login_with_org_btn, timeout=90)
        self.sb.wait_for_element_present(LogInLocators.org_title_h1, timeout=20)
        # type org_name
        self.sb.type(LogInLocators.org_input, org)
        self.sb.click(LogInLocators.continue_btn)
        # click "Continue with AAD"
        self.sb.wait_for_element_visible(LogInLocators.continue_aad_btn, timeout=10)
        self.sb.click(LogInLocators.continue_aad_btn)
        # Wait for redirecting to okta login page
        self.sb.wait_for_element_present(LogInLocators.microsoft_login_header, timeout=20)
        self.sb.type(LogInLocators.microsoft_email_input, user)
        self.sb.click(LogInLocators.next_btn)
        self.sb.wait_for_element_visible(LogInLocators.microsoft_pwd_input, timeout=20)

         # Type password and sign in
        self.sb.type(LogInLocators.microsoft_pwd_input, password)
        self.sb.attach_allure_screenshot("LoginAADSSO")
        # click "LogIn" to login ADD and stay signed in
        self.sb.click(LogInLocators.next_btn)
        self.sb.wait_for_element_present(LogInLocators.stay_signed_in_div)
        self.sb.click(LogInLocators.next_btn)
        self.sb.wait_for_element_visible(CloudHomeLocators.tgcloud_icon, timeout=60)
        self.sb.wait_for_element_present(CloudHomeLocators.org_name_div.format(org), timeout=30)
        self.sb.attach_allure_screenshot(f"Login to {org} with Okta user {user} successfully")

    @allure.step("Login with google sso successfully")
    def login_google(self, org, user, password):
        self.sb.click(LogInLocators.login_btn)
        self.sb.type(LogInLocators.org_input, org)
        self.sb.click(LogInLocators.continue_btn)
        # click "Continue with Google"
        self.sb.click(LogInLocators.google_continue_btn)
        self.sb.wait_for_element_present(LogInLocators.google_user_input)
        self.sb.click(LogInLocators.google_user_input)
        self.sb.type(LogInLocators.google_user_input, user)
        self.sb.click(LogInLocators.google_next_btn)
        self.sb.wait_for_element_present("//div[@data-email='" + user + "']", timeout=common_settings.WAIT_CLICK_TIME_OUT)
        self.sb.type(LogInLocators.google_pwd_input, password)
        self.sb.click(LogInLocators.google_next_btn)
        self.sb.wait_for_element_present("//div[contains(.,'" + org + "')]", timeout=common_settings.WAIT_RENDER_TIME_OUT)
        self.sb.attach_allure_screenshot("Login to {} with user {} successfully")

    @allure.step("Check account info")
    def check_account_info(self, user):
        LOGGER.info("Start to check account info")
        self.sb.click_if_visible(CloudHomeLocators.user_info_span.format(user), timeout=common_settings.WAIT_RENDER_TIME_OUT)
        self.sb.attach_allure_screenshot("UserProfile")

    @allure.step("Switch ORG")
    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=32)
    def switch_org(self, org):
        LOGGER.info("start to test switch org")
        self.sb.refresh()
        self.sb.wait_for_element_present(CloudHomeLocators.org_list_dropdown_btn, timeout=common_settings.WAIT_RENDER_TIME_OUT)

        # check current org id
        self.sb.click(CloudHomeLocators.org_list_dropdown_btn)
        self.sb.click_if_visible(CloudHomeLocators.switch_org_btn)
        self.sb.wait_for_element_present(CloudHomeLocators.org_panel_h5)

        current_org = self.sb.get_text(CloudHomeLocators.current_org_label)
        LOGGER.info(f"Current org is {current_org}")

        # switch org
        self.sb.hover(CloudHomeLocators.org_option_li.format(org))
        display_name = self.sb.get_text(CloudHomeLocators.display_name_tr.format(org))
        self.sb.click(CloudHomeLocators.org_option_li.format(org) + "//button")

        self.sb.wait_for_element_present(CloudHomeLocators.org_name_div.format(display_name), timeout=common_settings.WAIT_RENDER_TIME_OUT)
        LOGGER.info(f"Switch to org {org} successfully")
        self.sb.attach_allure_screenshot("Switch Org Successfully")

    @allure.step("Logout cloud V4")
    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=32)
    def logout(self):
        LOGGER.info("start to test logout")
        self.sb.wait_for_element_clickable(CloudHomeLocators.account_icon, timeout=common_settings.WAIT_RENDER_TIME_OUT)
        self.sb.click(CloudHomeLocators.account_icon, timeout=common_settings.WAIT_CLICK_TIME_OUT)
        self.sb.wait_for_element_clickable(CloudHomeLocators.common_div_button.format("Logout"), timeout=common_settings.WAIT_RENDER_TIME_OUT)
        self.sb.click(CloudHomeLocators.common_div_button.format("Logout"), timeout=common_settings.WAIT_CLICK_TIME_OUT)
        self.sb.wait_for_element_visible(LogInLocators.user_input, timeout=common_settings.WAIT_RENDER_TIME_OUT)
        # self.sb.assert_title(LogInLocators.login_title)
        LOGGER.info("Logout successfully")
        self.sb.attach_allure_screenshot("Logout successfully")

    def check_role(self, role):
        self.sb.click(LogOutLocators.account_icon)
        self.sb.assert_element_present(CloudHomeLocators.account_info_email.format(role))

    def get_all_org(self):
        """
        Get all org name in org list. (ORG list only show logs which user has the same login way)
        """
        LOGGER.info("Start to get all orgs for the user")
        self.sb.wait_for_element_present(CloudHomeLocators.org_list_dropdown_btn)
        # click ORG list dropdown
        self.sb.click(CloudHomeLocators.org_list_dropdown_btn)

        # click [Switch Organization] button
        self.sb.click_if_visible(CloudHomeLocators.switch_org_btn)
        self.sb.wait_for_element_present(CloudHomeLocators.org_panel_h5)

        # get all ORGs for the user
        orgs = self.sb.find_visible_elements(CloudHomeLocators.org_list_tr)
        orgs_name = []
        for i in range(1, len(orgs)):
            org_name = self.sb.get_text(CloudHomeLocators.org_list_tr + '[{}]'.format(i) + "/td[2]")
            orgs_name.append(org_name)
        LOGGER.info(f"Other orgs for the user are {orgs_name}")
        return orgs_name

    def check_last_login(self, org, user, pwd):
        """
        Check login org if the last login one
        """
        self.lobby_login(user, pwd)
        self.sb.assert_element_present(CloudHomeLocators.org_name_div.format(org))
        LOGGER.info(f"The login org is the last login one")


    @allure.step("Switch cloud v4 to v3")
    def switch_v4_to_v3(self):
        LOGGER.info("start to test switch cloud version on cloud portal")
        self.sb.refresh()
        self.sb.wait_for_element_present(CloudHomeLocators.org_list_dropdown_btn, timeout=common_settings.WAIT_RENDER_TIME_OUT)

        # check current org id
        self.sb.click(CloudHomeLocators.org_list_dropdown_btn)
        self.sb.click_if_visible(CloudHomeLocators.go_to_legacy_cloud)
        self.sb.wait_for_ready_state_complete()

        # check switch to cloud v3 successful
        self.sb.wait_for_element_present(CloudHomeLocators.my_cluster, timeout=common_settings.WAIT_CREATE_WS_TIME_OUT_200)
        LOGGER.info(f"Switch to legacy cloud successfully")
        self.sb.attach_allure_screenshot("Switch to Cloud v3 Successfully")

    @allure.step("Switch cloud v3 to v4")
    def switch_v3_to_v4(self):
        LOGGER.info("start to test switch cloud version on cloud portal")
        self.sb.wait_for_element_present(CloudHomeLocators.try_cloud_v4, timeout=common_settings.WAIT_CLICK_TIME_OUT)

        # click try cloud v4
        self.sb.click(CloudHomeLocators.try_cloud_v4)
        self.sb.wait_for_ready_state_complete()

        # check switch to cloud v3 successful
        self.sb.wait_for_element_present(WorkgroupLocators.workgroup_line, timeout=common_settings.WAIT_CREATE_WS_TIME_OUT_200)
        LOGGER.info(f"Switch to cloud V4 successfully")
        self.sb.attach_allure_screenshot("Switch to Cloud v4 Successfully")






