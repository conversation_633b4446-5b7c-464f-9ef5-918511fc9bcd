"""
Run workspace test cases
"""

from base.cloud_basecase import CloudBaseCase
from pages.cloud.workgroup.workgroup_home_page import WorkgroupHomePage
import allure
import pytest
from parameterized import parameterized
from utils.data_util.data_resolver import read_test_data

class TestWorkspace(CloudBaseCase):
    read_data = read_test_data(file="workspace_toolsbar_test_data.json")
    create_widget_test_data = read_data.get("toolsbar")
    test_data = []
    for i in create_widget_test_data:
        test_data.append(
          (
            i.get("toolsbar_name"),
            i.get("assert_texts")
          )
        )
    @allure.title("design schema - smoke test")
    @allure.description(
      "TestType: Positive \n"
      "Target: test RW workspace connect design schema \n"
      "Description: test RW workspace connect design schema \n"
      "TestDesigner: Song Sun \n"
      "Date: 2024-05-11 \n"
      "Link: https://graphsql.atlassian.net/browse/TCE-4380"
      )
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.cloud_v4
    @pytest.mark.connect
    @pytest.mark.workspace
    @pytest.mark.run(order=10)
    def test_connect_design_schema(self):
        whp = WorkgroupHomePage(self)
        whp.cloud_v4_login()
        whp.connect_design_schema()

    @allure.title("load data - smoke test")
    @allure.description(
      "TestType: Positive \n"
      "Target: test RW workspace connect load data \n"
      "Description: test RW workspace connect load data \n"
      "TestDesigner: Song Sun \n"
      "Date: 2024-05-11 \n"
      "Link: https://graphsql.atlassian.net/browse/TCE-4380"
      )
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.cloud_v4
    @pytest.mark.basecases
    @pytest.mark.connect
    @pytest.mark.workspace
    @pytest.mark.run(order=2)
    def test_connect_load_data(self):
        whp = WorkgroupHomePage(self)
        whp.cloud_v4_login()
        whp.connect_load_data()

    # @allure.title("Query Editor load 100M data - smoke test")
    # @allure.description("test RW workspace Query Editor load 100M data")
    # @pytest.mark.high
    # @pytest.mark.smoke
    # @pytest.mark.cloud_v4
    # @pytest.mark.basecases
    # @pytest.mark.connect
    # @pytest.mark.workspace
    # @pytest.mark.run(order=2.1)
    # def test_connect_gsql_editor_load_data(self):
    #     whp = WorkgroupHomePage(self)
    #     whp.cloud_v4_login()
    #     whp.connect_gsql_editor_load_data()


    @allure.title("Query Editor - smoke test")
    @allure.description(
        "TestType: Positive \n"
        "Target: test RW workspace connect Query Editor \n"
        "Description: test RW workspace connect Query Editor \n"
        "TestDesigner: Song Sun \n"
        "Date: 2024-05-11 \n"
        "Link: https://graphsql.atlassian.net/browse/TCE-4380"
        )
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.cloud_v4
    @pytest.mark.connect
    @pytest.mark.workspace
    @pytest.mark.run(order=3)
    def test_connect_gsql_editor(self):
        whp = WorkgroupHomePage(self)
        whp.cloud_v4_login()
        whp.connect_gsql_editor()

    @allure.title("Explore Graph - smoke test")
    @allure.description(
        "TestType: Positive \n"
        "Target: test RW workspace connect Explore Graph \n"
        "Description: test RW workspace connect Explore Graph \n"
        "TestDesigner: Song Sun \n"
        "Date: 2024-05-11 \n"
        "Link: https://graphsql.atlassian.net/browse/TCE-4380"
        )
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.cloud_v4
    @pytest.mark.connect
    @pytest.mark.workspace
    @pytest.mark.run(order=4)
    def test_connect_explore_graph(self):
        whp = WorkgroupHomePage(self)
        whp.cloud_v4_login()
        whp.connect_explore_graph()

    # # disable the case because it's a known issue.
    # @allure.title("Get Data Profile - smoke test")
    # @allure.description(
    #     "TestType: Positive \n"
    #     "Target: test RW workspace connect Get Data Profile \n"
    #     "Description: test RW workspace connect Get Data Profile \n"
    #     "TestDesigner: Song Sun \n"
    #     "Date: 2024-05-11 \n"
    #     "Link: https://graphsql.atlassian.net/browse/TCE-4380"
    #     )
    # @pytest.mark.high
    # @pytest.mark.smoke
    # @pytest.mark.cloud_v4
    # @pytest.mark.connect
    # @pytest.mark.workspace
    # @pytest.mark.run(order=5)
    # def test_connect_get_data_profile(self):
    #     whp = WorkgroupHomePage(self)
    #     whp.cloud_v4_login()
    #     whp.connect_get_data_profile()


    @allure.title("connect solution - smoke test")
    @allure.description(
        "TestType: Positive \n"
        "Target: test RW workspace connect solution \n"
        "Description: test RW workspace connect solution \n"
        "TestDesigner: Song Sun \n"
        "Date: 2024-05-11 \n"
        "Link: https://graphsql.atlassian.net/browse/TCE-4380"
        )
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.cloud_v4
    @pytest.mark.connect
    @pytest.mark.workspace
    @pytest.mark.run(order=99999) # will install query and last to run
    def test_connect_solution(self):
        whp = WorkgroupHomePage(self)
        whp.cloud_v4_login()
        whp.connect_solution()


    @allure.title("connect from API - smoke test")
    @allure.description(
        "TestType: Positive \n"
        "Target: test RW workspace connect from API \n"
        "Description: test RW workspace connect from API \n"
        "TestDesigner: Song Sun \n"
        "Date: 2024-05-11 \n"
        "Link: https://graphsql.atlassian.net/browse/TCE-4380"
        )
    @pytest.mark.skip(reason="Need rewrite")
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.cloud_v4
    @pytest.mark.connect
    @pytest.mark.workspace
    @pytest.mark.run(order=7)
    def test_connect_from_API(self):
        whp = WorkgroupHomePage(self)
        whp.cloud_v4_login()
        whp.connect_from_API()


    @allure.title("connect from addons(Admin Portal)  - smoke test")
    @allure.description(
      "TestType: Positive \n"
      "Target: test RW workspace connect from addons(Admin Portal) \n"
      "Description: test RW workspace connect from addons(Admin Portal) \n"
      "TestDesigner: Song Sun \n"
      "Date: 2024-05-11 \n"
      "Link: https://graphsql.atlassian.net/browse/TCE-4380"
      )
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.cloud_v4
    @pytest.mark.connect
    @pytest.mark.workspace
    @pytest.mark.onlinecheck
    @pytest.mark.run(order=8)
    def test_connect_from_addons_GAP(self):
        whp = WorkgroupHomePage(self)
        whp.cloud_v4_login()
        whp.connect_from_addons(tools_name="Admin Portal")

    @allure.title("connect from addons(Admin Portal) and search log  - smoke test")
    @allure.description(
      "TestType: Positive \n"
      "Target: test RW workspace connect from addons(Admin Portal) and search log \n"
      "Description: test RW workspace connect from addons(Admin Portal) and search log \n"
      "TestDesigner: Song Sun \n"
      "Date: 2024-11-28 \n"
      "Link: https://graphsql.atlassian.net/browse/TCE-4380"
      )
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.cloud_v4
    @pytest.mark.connect
    @pytest.mark.workspace
    @pytest.mark.run(order=8)
    def test_search_log_in_GAP(self):
        whp = WorkgroupHomePage(self)
        whp.cloud_v4_login()
        whp.connect_from_addons(tools_name="Admin Portal", search_log=True)


    @allure.title("connect from addons(TigerGraph Insights)  - smoke test")
    @allure.description(
      "TestType: Positive \n"
      "Target: test RW workspace connect from addons(TigerGraph Insights) \n"
      "Description: test RW workspace connect from addons(TigerGraph Insights) \n"
      "TestDesigner: Song Sun \n"
      "Date: 2024-05-11 \n"
      "Link: https://graphsql.atlassian.net/browse/TCE-4380"
      )
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.cloud_v4
    @pytest.mark.connect
    @pytest.mark.workspace
    @pytest.mark.onlinecheck
    @pytest.mark.run(order=8.1)
    def test_connect_from_addons_insights(self):
        whp = WorkgroupHomePage(self)
        whp.cloud_v4_login()
        whp.connect_from_addons(tools_name="TigerGraph Insights")

    @allure.title("connect from addons(TigerGraph GraphStudio)  - smoke test")
    @allure.description(
        "TestType: Positive \n"
        "Target: test RW workspace connect from addons(TigerGraph GraphStudio) \n"
        "Description: test RW workspace connect from addons(TigerGraph GraphStudio) \n"
        "TestDesigner: Song Sun \n"
        "Date: 2024-05-11 \n"
        "Link: https://graphsql.atlassian.net/browse/TCE-4380"
        )
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.cloud_v4
    @pytest.mark.connect
    @pytest.mark.workspace
    @pytest.mark.onlinecheck
    @pytest.mark.run(order=8.2)
    def test_connect_from_addons_GST(self):
        whp = WorkgroupHomePage(self)
        whp.cloud_v4_login()
        whp.connect_from_addons(tools_name="TigerGraph GraphStudio")


    @allure.title("monitor  - smoke test")
    @allure.description(
        "TestType: Positive \n"
        "Target: test RW workspace monitor \n"
        "Description: test RW workspace monitor \n"
        "TestDesigner: Song Sun \n"
        "Date: 2024-05-11 \n"
        "Link: https://graphsql.atlassian.net/browse/TCE-4380"
        )
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.cloud_v4
    @pytest.mark.connect
    @pytest.mark.workspace
    @pytest.mark.onlinecheck
    @pytest.mark.run(order=11)
    def test_monitor(self):
        whp = WorkgroupHomePage(self)
        whp.cloud_v4_login()
        whp.monitor_ws()

    @allure.title("toolsbar for WS - smoke test")
    @allure.description(
        "TestType: Positive \n"
        "Target: test toolsbar for WS \n"
        "Description: test toolsbar for WS \n"
        "TestDesigner: Song Sun \n"
        "Date: 2024-05-11 \n"
        "Link: https://graphsql.atlassian.net/browse/TCE-4380"
        )
    @parameterized.expand(test_data)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.cloud_v4
    @pytest.mark.connect
    @pytest.mark.workspace
    @pytest.mark.run(order=12)
    def test_toolsbar_for_WS(self, toolsbar_name, assert_texts):
        whp = WorkgroupHomePage(self)
        whp.cloud_v4_login()
        whp.toolsbar_for_WS(tab_name=toolsbar_name, assert_texts=assert_texts)

    @allure.title("check access management for workspace admin user  - smoke test")
    @allure.description(
        "TestType: Positive \n"
        "Target: check access management for workspace admin user \n"
        "Description: check access management for workspace admin user \n"
        "TestDesigner: Song Sun \n"
        "Date: 2024-12-06 \n"
        "Link: https://graphsql.atlassian.net/browse/TCE-5913"
        )
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.cloud_v4
    @pytest.mark.connect
    @pytest.mark.workspace
    @pytest.mark.run(order=13)
    def test_access_management_with_workspace_admin(self):
        whp = WorkgroupHomePage(self)
        whp.cloud_v4_login()
        whp.grant_workspace_role()
        whp.check_workspace_role_privillege()

    @allure.title("check access management for workspace member user  - smoke test")
    @allure.description(
        "TestType: Positive \n"
        "Target: check access management for workspace member user \n"
        "Description: check access management for workspace member user \n"
        "TestDesigner: Song Sun \n"
        "Date: 2024-12-06 \n"
        "Link: https://graphsql.atlassian.net/browse/TCE-5913"
        )
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.cloud_v4
    @pytest.mark.connect
    @pytest.mark.workspace
    @pytest.mark.run(after="test_access_management_with_workspace_admin")
    @pytest.mark.run(order=140)
    def test_access_management_with_workspace_member(self):
        whp = WorkgroupHomePage(self)
        whp.cloud_v4_login()
        whp.grant_workspace_role(role="Workspace Member")
        whp.check_workspace_role_privillege(role="Workspace Member")

    @allure.title("input special character to cron value - smoke test")
    @allure.description(
        "TestType: Negative \n"
        "Target: input special character to cron value  \n"
        "Description: input special character to cron value  \n"
        "TestDesigner: Song Sun \n"
        "Date: 2024-12-06 \n"
        "Link: https://graphsql.atlassian.net/browse/TCE-5913"
        )
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.cloud_v4
    @pytest.mark.connect
    @pytest.mark.workspace
    @pytest.mark.run(order=14)
    def test_input_special_character_to_backup_cron(self):
        whp = WorkgroupHomePage(self)
        whp.cloud_v4_login()
        whp.input_sepcial_characters_case()



    @allure.title("input special character to network access - smoke test")
    @allure.description(
        "TestType: Negative \n"
        "Target: input special character to network access  \n"
        "Description: input special character to network access  \n"
        "TestDesigner: Song Sun \n"
        "Date: 2024-12-06 \n"
        "Link: https://graphsql.atlassian.net/browse/TCE-5913"
        )
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.cloud_v4
    @pytest.mark.connect
    @pytest.mark.teardown
    @pytest.mark.workspace
    @pytest.mark.run(order=1)
    def test_input_special_character_to_network_access(self):
        whp = WorkgroupHomePage(self)
        whp.cloud_v4_login()
        whp.input_sepcial_characters_case(tab="Network Access")

    @allure.title("load data from S3")
    @allure.description(
        "TestType: Positive \n"
        "Target: test RW workspace load data from S3 \n"
        "Description: test RW workspace load data from S3 \n"
        "TestDesigner: Yaxin Li \n"
        "Date: 2024-12-24 \n"
        "Link: https://graphsql.atlassian.net/browse/TCE-6149"
    )
    @pytest.mark.high
    @pytest.mark.release
    @pytest.mark.cloud_v4
    @pytest.mark.connect
    @pytest.mark.workspace
    @pytest.mark.run(order=0.2)
    def test_connect_load_data_from_s3(self):
        whp = WorkgroupHomePage(self)
        whp.cloud_v4_login()
        whp.connect_load_data_from_s3()

    @allure.title("connect load data from GCS")
    @allure.description(
        "TestType: Positive \n"
        "Target: test RW workspace load data from GCS \n"
        "Description: test RW workspace load data from GCS \n"
        "TestDesigner: Yaxin Li \n"
        "Date: 2024-12-24 \n"
        "Link: https://graphsql.atlassian.net/browse/TCE-6149"
    )
    @pytest.mark.high
    @pytest.mark.release
    @pytest.mark.cloud_v4
    @pytest.mark.connect
    @pytest.mark.workspace
    @pytest.mark.run(order=3)
    def test_connect_load_data_from_gcs(self):
        whp = WorkgroupHomePage(self)
        whp.cloud_v4_login()
        whp.connect_load_data_from_gcs()

    @allure.title("connect load data from Azure Blob Storage")
    @allure.description(
        "TestType: Positive \n"
        "Target: test RW workspace load data from Azure Blob Storage \n"
        "Description: test RW workspace load data from Azure Blob Storage \n"
        "TestDesigner: Yaxin Li \n"
        "Date: 2024-12-24 \n"
        "Link: https://graphsql.atlassian.net/browse/TCE-6149"
    )
    @pytest.mark.high
    @pytest.mark.release
    @pytest.mark.cloud_v4
    @pytest.mark.connect
    @pytest.mark.workspace
    @pytest.mark.run(order=3)
    def test_connect_load_data_from_azure_blob(self):
        whp = WorkgroupHomePage(self)
        whp.cloud_v4_login()
        whp.connect_load_data_from_abs()

    @allure.title("Query Editor query vertex and edge count")
    @allure.description(
        "TestType: Positive \n"
        "Target: test RW workspace Query Editor query vertex and edge count \n"
        "Description: test RW workspace Query Editor query vertex and edge count \n"
        "TestDesigner: Yaxin Li \n"
        "Date: 2024-01-02 \n"
        "Link: https://graphsql.atlassian.net/browse/TCE-6149"
    )
    @pytest.mark.high
    @pytest.mark.release
    @pytest.mark.cloud_v4
    @pytest.mark.connect
    @pytest.mark.workspace
    @pytest.mark.run(after="test_connect_load_data_from_s3")
    @pytest.mark.skip(reason="UI is unstable, temporarily skip this case.")
    def test_connect_gsql_editor_query(self):
        whp = WorkgroupHomePage(self)
        whp.cloud_v4_login()
        whp.connect_gsql_editor_query_count()

    @allure.title("RW workspace explore graph")
    @allure.description(
          "TestType: Positive \n"
          "Target: test RW workspace explore graph \n"
          "Description: test RW workspace explore graph \n"
          "TestDesigner: Yaxin Li \n"
          "Date: 2024-01-02 \n"
          "Link: https://graphsql.atlassian.net/browse/TCE-6149"
    )
    @pytest.mark.high
    @pytest.mark.release
    @pytest.mark.cloud_v4
    @pytest.mark.connect
    @pytest.mark.workspace
    @pytest.mark.run(after="test_connect_load_data_from_s3")
    @pytest.mark.skip(reason="UI is unstable, temporarily skip this case.")
    def test_explore_graph_phone_vertex(self):
        whp = WorkgroupHomePage(self)
        whp.cloud_v4_login()
        whp.test_connect_explore_graph_phone_vertex()

    @allure.title("monitor metrics")
    @allure.description(
        "TestType: Positive \n"
        "Target: test RW workspace monitor metrics \n"
        "Description: test RW workspace monitor metrics \n"
        "TestDesigner: Yaxin Li \n"
        "Date: 2025-01-02 \n"
        "Link: https://graphsql.atlassian.net/browse/TCE-6149"
    )
    @pytest.mark.high
    @pytest.mark.release
    @pytest.mark.cloud_v4
    @pytest.mark.connect
    @pytest.mark.workspace
    @pytest.mark.run(order=0.2)
    def test_monitor_metrics(self):
        whp = WorkgroupHomePage(self)
        whp.cloud_v4_login()
        whp.verify_monitor_metrics()

    @allure.title("connect load data from local file")
    @allure.description(
        "TestType: Positive \n"
        "Target: test RW workspace load data from local file \n"
        "Description: test RW workspace load data from local file \n"
        "TestDesigner: Yaxin Li \n"
        "Date: 2024-1-06 \n"
        "Link: https://graphsql.atlassian.net/browse/TCE-6149"
    )
    @pytest.mark.high
    @pytest.mark.release
    @pytest.mark.loaddata
    @pytest.mark.cloud_v4
    @pytest.mark.connect
    @pytest.mark.workspace
    @pytest.mark.run(order=3)
    def test_connect_load_data_from_local(self):
        whp = WorkgroupHomePage(self)
        whp.cloud_v4_login()
        whp.connect_load_data_from_local()

    @allure.title("connect load data from Delta Lake")
    @allure.description(
        "TestType: Positive \n"
        "Target: test RW workspace load data from Delta Lake \n"
        "Description: test RW workspace load data from Delta Lake \n"
        "TestDesigner: Yaxin Li \n"
        "Date: 2025-01-06 \n"
        "Link: https://graphsql.atlassian.net/browse/TCE-6149"
    )
    @pytest.mark.high
    @pytest.mark.release
    @pytest.mark.loaddata
    @pytest.mark.cloud_v4
    @pytest.mark.connect
    @pytest.mark.workspace
    @pytest.mark.run(order=3)
    def test_connect_load_data_from_delta_lake(self):
        whp = WorkgroupHomePage(self)
        whp.cloud_v4_login()
        whp.connect_load_data_from_delta_lake()

    @allure.title("connect load data from Iceberg")
    @allure.description(
        "TestType: Positive \n"
        "Target: test RW workspace load data template from Iceberg \n"
        "Description: test RW workspace load data template from Iceberg \n"
        "TestDesigner: Yaxin Li \n"
        "Date: 2024-1-06 \n"
        "Link: https://graphsql.atlassian.net/browse/TCE-6149"
    )
    @pytest.mark.high
    @pytest.mark.release
    @pytest.mark.loaddata
    @pytest.mark.cloud_v4
    @pytest.mark.connect
    @pytest.mark.workspace
    @pytest.mark.run(order=3)
    def test_connect_load_data_from_iceberg(self):
        whp = WorkgroupHomePage(self)
        whp.cloud_v4_login()
        whp.connect_load_data_from_iceberg()

    @allure.title("connect load data from Snowflake")
    @allure.description(
        "TestType: Positive \n"
        "Target: test RW workspace load data template from Snowflake \n"
        "Description: test RW workspace load data template from Snowflake \n"
        "TestDesigner: Yaxin Li \n"
        "Date: 2024-1-06 \n"
        "Link: https://graphsql.atlassian.net/browse/TCE-6149"
    )
    @pytest.mark.high
    @pytest.mark.release
    @pytest.mark.loaddata
    @pytest.mark.cloud_v4
    @pytest.mark.connect
    @pytest.mark.workspace
    @pytest.mark.run(order=2)
    def test_connect_load_data_from_snowflake(self):
        whp = WorkgroupHomePage(self)
        whp.cloud_v4_login()
        whp.connect_load_data_from_snowflake()

    @allure.title("connect load data from Spark")
    @allure.description(
        "TestType: Positive \n"
        "Target: test RW workspace load data template from Spark \n"
        "Description: test RW workspace load data template from Spark \n"
        "TestDesigner: Yaxin Li \n"
        "Date: 2024-1-06 \n"
        "Link: https://graphsql.atlassian.net/browse/TCE-6149"
    )
    @pytest.mark.high
    @pytest.mark.release
    @pytest.mark.loaddata
    @pytest.mark.cloud_v4
    @pytest.mark.connect
    @pytest.mark.workspace
    @pytest.mark.run(order=3)
    def test_connect_load_data_from_spark(self):
        whp = WorkgroupHomePage(self)
        whp.cloud_v4_login()
        whp.connect_load_data_from_spark()

    @allure.title("connect load data from Kafka")
    @allure.description(
        "TestType: Positive \n"
        "Target: test RW workspace load data template from Kafka \n"
        "Description: test RW workspace load data template from Kafka \n"
        "TestDesigner: Yaxin Li \n"
        "Date: 2024-1-06 \n"
        "Link: https://graphsql.atlassian.net/browse/TCE-6149"
    )
    @pytest.mark.high
    @pytest.mark.release
    @pytest.mark.loaddata
    @pytest.mark.cloud_v4
    @pytest.mark.connect
    @pytest.mark.workspace
    @pytest.mark.run(order=3)
    def test_connect_load_data_from_kafka(self):
        whp = WorkgroupHomePage(self)
        whp.cloud_v4_login()
        whp.connect_load_data_from_kafka()

    @allure.title("connect load data from Postgres")
    @allure.description(
        "TestType: Positive \n"
        "Target: test RW workspace load data template from Postgres \n"
        "Description: test RW workspace load data template from Postgres \n"
        "TestDesigner: Yaxin Li \n"
        "Date: 2024-1-06 \n"
        "Link: https://graphsql.atlassian.net/browse/TCE-6149"
    )
    @pytest.mark.high
    @pytest.mark.release
    @pytest.mark.loaddata
    @pytest.mark.cloud_v4
    @pytest.mark.connect
    @pytest.mark.workspace
    @pytest.mark.run(order=3)
    def test_connect_load_data_from_postgres(self):
        whp = WorkgroupHomePage(self)
        whp.cloud_v4_login()
        whp.connect_load_data_from_postgres()

    @allure.title("connect load data from BigQuery")
    @allure.description(
        "TestType: Positive \n"
        "Target: test RW workspace load data template from BigQuery \n"
        "Description: test RW workspace load data template from BigQuery \n"
        "TestDesigner: Yaxin Li \n"
        "Date: 2024-1-06 \n"
        "Link: https://graphsql.atlassian.net/browse/TCE-6149"
    )
    @pytest.mark.high
    @pytest.mark.release
    @pytest.mark.loaddata
    @pytest.mark.cloud_v4
    @pytest.mark.connect
    @pytest.mark.workspace
    @pytest.mark.run(order=0.2)
    def test_connect_load_data_from_bigquery(self):
        whp = WorkgroupHomePage(self)
        whp.cloud_v4_login()
        whp.connect_load_data_from_bigquery()

    @allure.title("create solution workspace")
    @allure.description(
        "TestType: Positive \n"
        "Target: test create solution workspace with Mule Account Detection \n"
        "Description: test create solution workspace with Mule Account Detection template \n"
        "TestDesigner: Yaxin Li \n"
        "Date: 2024-1-08 \n"
        "Link: https://graphsql.atlassian.net/browse/TCE-6149"
    )
    @pytest.mark.high
    @pytest.mark.release
    @pytest.mark.loaddata
    @pytest.mark.cloud_v4
    @pytest.mark.preexec
    @pytest.mark.workspace
    @pytest.mark.run(order=0.2)
    def test_create_solution_workspace(self):
        whp = WorkgroupHomePage(self)
        whp.cloud_v4_login()
        whp.create_solution_workspace()

    @allure.title("run Mule Account Detection query")
    @allure.description(
        "TestType: Positive \n"
        "Target: test run Mule Account Detection query\n"
        "Description: test run query with Mule Account Detection \n"
        "TestDesigner: Yaxin Li \n"
        "Date: 2024-1-08 \n"
        "Link: https://graphsql.atlassian.net/browse/TCE-6149"
    )
    @pytest.mark.high
    @pytest.mark.release
    @pytest.mark.loaddata
    @pytest.mark.cloud_v4
    @pytest.mark.connect
    @pytest.mark.workspace
    @pytest.mark.run(after="test_create_solution_workspace")
    def test_run_solution_workspace(self):
        whp = WorkgroupHomePage(self)
        whp.cloud_v4_login()
        whp.verify_mule_detection_query()

    @allure.title("delete solution workspace")
    @allure.description(
        "TestType: Positive \n"
        "Target: test delete solution workspace\n"
        "Description: test delete solution workspace \n"
        "TestDesigner: Yaxin Li \n"
        "Date: 2024-1-08 \n"
        "Link: https://graphsql.atlassian.net/browse/TCE-6149"
    )
    @pytest.mark.high
    @pytest.mark.release
    @pytest.mark.loaddata
    @pytest.mark.cloud_v4
    @pytest.mark.connect
    @pytest.mark.workspace
    @pytest.mark.run(after="test_run_solution_workspace")
    def test_run_solution_workspace(self):
        whp = WorkgroupHomePage(self)
        whp.cloud_v4_login()
        whp.check_workgroup_and_delete_if_existed(work_group_name="solution_workgroup", workspace_name="solution_workspace")

    @allure.title("check shared file in Query Editor")
    @allure.description(
        "TestType: Positive \n"
        "Target: test check shared file in Query Editor \n"
        "Description: test check shared file in Query Editor \n"
        "TestDesigner: Yaxin Li \n"
        "Date: 2024-01-09 \n"
        "Link: https://graphsql.atlassian.net/browse/TCE-6331"
    )
    @pytest.mark.high
    @pytest.mark.release
    @pytest.mark.cloud_v4
    @pytest.mark.connect
    @pytest.mark.workspace
    @pytest.mark.run(order=3.1)
    def test_check_shared_file_in_gsql_editor(self):
        whp = WorkgroupHomePage(self)
        whp.cloud_v4_login()
        whp.check_shared_file_in_gsql_editor()

    @allure.title("check Query Tutorials files")
    @allure.description(
        "TestType: Positive \n"
        "Target: test check Query Tutorials files \n"
        "Description: test check Query Tutorials files including 00_schema.gsql and 01_load.gsql \n"
        "TestDesigner: Yaxin Li \n"
        "Date: 2024-03-05 \n"
        "Link: https://graphsql.atlassian.net/browse/TCE-6353"
    )
    @pytest.mark.high
    @pytest.mark.release
    @pytest.mark.cloud_v4
    @pytest.mark.connect
    @pytest.mark.workspace
    @pytest.mark.run(order=3.2)
    def test_check_gsql_tutorials_files(self):
        whp = WorkgroupHomePage(self)
        whp.cloud_v4_login()
        whp.check_gsql_tutorials_files()

    @allure.title("check Cypher Tutorials files")
    @allure.description(
        "TestType: Positive \n"
        "Target: test check Cypher Tutorials files \n"
        "Description: test check Cypher Tutorials files including c0_schema.cypher and c0_load.cypher \n"
        "TestDesigner: Yaxin Li \n"
        "Date: 2024-03-05 \n"
        "Link: https://graphsql.atlassian.net/browse/TCE-6353"
    )
    @pytest.mark.high
    @pytest.mark.release
    @pytest.mark.cloud_v4
    @pytest.mark.connect
    @pytest.mark.workspace
    @pytest.mark.run(order=3.3)
    def test_check_cypher_tutorials_files(self):
        whp = WorkgroupHomePage(self)
        whp.cloud_v4_login()
        whp.check_cypher_tutorials_files()

    @allure.title("Query Editor profile query")
    @allure.description(
        "TestType: Positive \n"
        "Target: test RW workspace Query Editor profile query \n"
        "Description: test RW workspace Query Editor profile query \n"
        "TestDesigner: Yaxin Li \n"
        "Date: 2024-04-17 \n"
        "Link: https://graphsql.atlassian.net/browse/TCE-6522"
    )
    @pytest.mark.high
    @pytest.mark.release
    @pytest.mark.cloud_v4
    @pytest.mark.connect
    @pytest.mark.workspace
    @pytest.mark.run(after="test_connect_gsql_editor_query")
    def test_connect_gsql_profile(self):
        whp = WorkgroupHomePage(self)
        whp.cloud_v4_login()
        whp.connect_gsql_profile()

    @allure.title("Prevent Event Propagation When Deleting Search Text")
    @allure.description(
        "TestType: Positive \n"
        "Target: test prevent event propagation when deleting search text \n"
        "Description: test prevent event propagation when deleting search text in search box \n"
        "TestDesigner: Yaxin Li \n"
        "Date: 2024-04-17 \n"
        "Link: https://graphsql.atlassian.net/browse/TCE-6522"
    )
    @pytest.mark.high
    @pytest.mark.release
    @pytest.mark.cloud_v4
    @pytest.mark.connect
    @pytest.mark.workspace
    @pytest.mark.run(order=10.1)
    def test_create_vertex(self):
        whp = WorkgroupHomePage(self)
        whp.cloud_v4_login()
        whp.create_vertex_in_schema()

    @allure.title("Verify Error Message When Creating Duplicate Vertex")
    @allure.description(
        "TestType: Negative \n"
        "Target: Verify that vertices with the same name cannot be created in the same graph \n"
        "Description: Create a vertex and then attempt to create another vertex with the same name, verify the error message \n"
        "TestDesigner: Yaxin Li \n"
        "Date: 2024-04-17 \n"
        "Link: https://graphsql.atlassian.net/browse/TCE-6522"
    )
    @pytest.mark.high
    @pytest.mark.release
    @pytest.mark.cloud_v4
    @pytest.mark.connect
    @pytest.mark.workspace
    @pytest.mark.run(order=10.2)
    def test_duplicate_vertex_error(self):
        whp = WorkgroupHomePage(self)
        whp.cloud_v4_login()
        whp.verify_duplicate_vertex_error()

    @allure.title("run insights query")
    @allure.description(
        "TestType: Positive \n"
        "Target: test run insights query\n"
        "Description: test run insights_get_net_gain_numbers_and_percentages query \n"
        "TestDesigner: Yaxin Li \n"
        "Date: 2024-04-17 \n"
        "Link: https://graphsql.atlassian.net/browse/TCE-6522"
    )
    @pytest.mark.high
    @pytest.mark.release
    @pytest.mark.loaddata
    @pytest.mark.cloud_v4
    @pytest.mark.connect
    @pytest.mark.workspace
    @pytest.mark.run(after="test_create_solution_workspace")
    def test_run_query_result(self):
        whp = WorkgroupHomePage(self)
        whp.cloud_v4_login()
        whp.verify_query_result()
