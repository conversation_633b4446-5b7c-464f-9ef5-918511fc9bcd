---
Rule Name: agent
Description: Cloud Portal Agent Development Guide and conventions for the codebase.
alwaysApply: true
---

# Cloud Portal Agent Development Guide

## Project Overview

Cloud Portal is a modern web application for cloud-based graph database management, built with React and TypeScript. The project emphasizes modularity, scalability, a consistent design system, robust async data management, and a great developer experience.

---

## Tech Stack & Key Libraries

- **React 18** + **TypeScript**
- **react-query**: Primary state management for API data (fetching, caching, mutation, auto-refresh)
- **React Context**: For global UI state (organization, workspace, theme, etc.)
- **@tigergraph/app-ui-lib**: Main UI component library, deeply customized from [baseweb](mdc:https:/baseweb.design), supporting our Design System and dark theme
- **@tigergraph/tools-models**: Core library for interacting with TigerGraph database APIs (cluster/workspace)
- **@tigergraph/tools-ui**: Business component library, especially for graph visualization and editing
- **shadcn**: Used for supplemental UI components only
- **styletron** (styled-components) + **Tailwind**: Styling solutions, but prefer styletron for consistency and theme support
- **react-router-dom v6**: Routing and navigation
- **Vitest**: Unit and UI testing
- **ESLint + Prettier**: Code style and formatting
- We use english for all the code, comments, documentation.

---

## Directory Structure

- `src/`: Main source code
  - `components/`: UI components (prefer @tigergraph/app-ui-lib)
  - `pages/`: Pages and routing, including layout.tsx for layout logic
  - `contexts/`: Global state management (Context)
  - `lib/`, `hooks/`, `utils/`: Utilities and custom hooks
- `tests/`: Test scripts and configuration
- `package.json`: Dependencies and scripts
- `README.md`, `AGENT.md`: Documentation

---

## API Layer & Data Flow

### Multiple Axios Instances

We use three separate axios instances to interact with different backend services:

- **controller**: For platform-level management APIs (`axiosController`)
- **org service**: For organization-related APIs (`axiosOrgService`)
- **cluster**: For workspace/TigerGraph database APIs (`axiosCluster`)

Each instance is configured with its own base URL and may have custom interceptors or headers as needed.

### API Response Types

- **Result**: Used for controller and org service APIs. Defined as:
  ```ts
  type Result<T = unknown> = {
    Error: boolean;
    ErrorDetails?: any;
    Message?: string;
    Result?: T;
  };
  ```
- **Return**: Used for cluster (TigerGraph database) APIs. Defined as:
  ```ts
  type Return<T = unknown> = {
    error: boolean;
    message: string;
    results?: T;
  };
  ```

---

## Contexts & State Management

### React Query

- **react-query** is the only recommended tool for API data state management, handling all async data fetching, caching, mutation, and auto-refresh.

### React Contexts

- **workspaceContext**: Manages the current workspace, available workspaces, graph names, DB user, pricing, cloud providers, feature flags, and org quota. It also synchronizes workspace selection with URL params and updates axios base URLs for cluster APIs.
- **orgContext**: Manages organization-level state, including user info, org list, current org, raw org data, onboarding tasks, and workgroup acquisition logic.
- **themeContext**: Handles theme switching and dark mode support.
- Other contexts exist for schema design, graph editing, etc.

Contexts are used for global UI state only, not for API data (which should use react-query).

---

## User Role Hierarchy

User roles are hierarchical and fine-grained, defined in `type.ts` and `models.ts`:

- **super-admins**: Organization-level admin, implicitly has all lower-level admin roles
- **workgroup-admins**: Admin for a specific workgroup, also admin for all workspaces in that workgroup
- **workspace-admins**: Admin for a specific workspace
- **workspace-users**: Regular user for a workspace
- **billing-admins** and **billing-viewers**: Special roles for billing management

Role checks are utility functions (e.g., `isOrgAdmin`, `isGroupAdmin`, `isSpaceAdmin`) and are used throughout the app for permission control and UI logic.

---

## UI Component System

- **@tigergraph/app-ui-lib**: The primary UI component library, deeply customized from baseweb. All general UI components (Button, Input, Table, Modal, Popover, ThemeProvider, etc.) should use this library to ensure design consistency and dark theme support.
- **@tigergraph/tools-ui**: Used for business-specific components, especially graph visualization and editing.
- **shadcn**: Used only as a supplement.
- **Tailwind** and **styletron** (styled-components) are both present, but styletron is preferred for consistency and theming.

---

## Theming & Dark Mode

- Theming and dark mode are implemented via `ThemeProvider` and `@tigergraph/app-ui-lib/Theme`. All custom components must be theme-compatible.
- Example:
  ```tsx
  import { useTheme } from '@/contexts/themeContext';
  const { theme, toggleTheme } = useTheme();
  ```

---

## Routing & Layout

- **App.tsx** is the main entry, wrapping the app with all global Providers (QueryClientProvider, ThemeProvider, OrgProvider, etc.).
- **layout.tsx** (e.g., `src/pages/home/<USER>
- Routing uses `react-router-dom v6`, supporting lazy loading and nested routes.

---

## API State and Feedback Patterns

We have established patterns for handling the lifecycle of async operations (loading, success, error) using `react-query` and providing user feedback with `react-hot-toast`.

### 1. Handling `useQuery` Failures with `<ErrorDisplay />`

For data fetching operations using `useQuery`, if an error occurs that prevents a page or a major component from rendering its content, we use the `<ErrorDisplay />` component. This provides a consistent and user-friendly way to show that something went wrong while loading data.

**Example:**

```tsx
const { data, isLoading, isError, error } = useQuery('my-data', fetchData);

if (isLoading) {
  return <LoadingIndicator />;
}

if (isError) {
  return <ErrorDisplay label="Server Error:" error={error} />;
}

return (
  // Render component with data
);
```

### 2. Declarative Feedback with `toast.promise`

For user-initiated actions (create, update, delete), we recommend using `react-hot-toast`'s `toast.promise` for clear and immediate feedback. It automatically handles loading, success, and error messages for a promise, reducing boilerplate and improving user experience. This is the preferred method for most mutation scenarios.

**Example:**

```tsx
import toast from 'react-hot-toast';

// ...

const onDelete = () => {
  const promise = deleteItemMutation.mutateAsync(itemId);
  toast.promise(promise, {
    loading: 'Deleting item...',
    success: 'Item deleted successfully!',
    error: (err) => `Error: ${getErrorMessage(err)}`,
  });
};
```

### 3. Handling Complex Mutations with `onSuccess`/`onError`

For mutations that require more complex logic in success or error handlers (e.g., redirecting the user, complex state updates beyond simple cache invalidation), using the `onSuccess` and `onError` callbacks directly in `useMutation` is a suitable alternative.

**Example (`react-hot-toast`):**

```tsx
import toast from 'react-hot-toast';

// ...

const { mutate, isLoading } = useMutation(updateData, {
  onSuccess: () => {
    toast.success('Configuration updated successfully');
    queryClient.invalidateQueries('my-data');
    // Additional complex logic here...
  },
  onError: (error) => {
    toast.error(getErrorMessage(error) || 'Failed to update configuration');
    // Additional complex logic here...
  },
});
```

### Summary & Best Practices

- **Use `<ErrorDisplay />`** for critical data fetching errors that block UI rendering.
- **Use `toast.promise`** as the primary method for providing feedback on mutations.
- **Use `onSuccess`/`onError` callbacks** only for mutations that require complex side-effect logic.
- While the legacy `showToast` function exists, **new code should prefer importing and using `react-hot-toast`** for consistency and access to richer features like `toast.promise`.

---

## Testing

- **Vitest** is used for all tests. Test files are colocated with source files and use the `.test.tsx` suffix.
- UI tests are recommended to use Testing Library.

---

## Main Development Commands

- Start dev server: `yarn dev` or `yarn dev:https`
- Build: `yarn build`, `yarn build:uat`, `yarn build:staging`, `yarn build:production`
- Run all tests: `yarn test`
- UI tests: `yarn test:ui`
- Single file test: `yarn test -- src/path/to/file.test.tsx`
- Lint: `yarn lint`, `yarn eslint`
- TypeScript check: `yarn typescript:check`
- Format code: `yarn run prettier`

---

## Code Style & Standards

- Single quotes, 2-space indentation, 120 character line limit
- Import order enforced by eslint-plugin-simple-import-sort
- All components are React functional components with TypeScript
- UI components should use @tigergraph/app-ui-lib, styles should prefer styletron
- Tests colocated with source, using Vitest

---

## Recommended Workflow & Best Practices

1. **Feature Development**: Always use react-query for API data, and prefer @tigergraph/app-ui-lib for UI components.
2. **Global State**: Use Context and Provider only for global UI state.
3. **Theme Compatibility**: All custom components must support dark mode.
4. **Pre-commit**: Ensure lint, type check, and tests all pass before committing.
5. **Testing**: All new features must have tests, colocated with the source file.

---

## References

- [Baseweb Documentation](mdc:https:/baseweb.design)
- [react-query Documentation](mdc:https:/tanstack.com/query/v3)
- [Vitest Documentation](mdc:https:/vitest.dev)

## UI Component Library Supplement

### Popover Component Usage

- `@tigergraph/app-ui-lib/popover`: Used for tooltips (simple hover hints). Only recommended for basic tooltip scenarios.
- `@tigergraph/app-ui-lib/popover2`: Used for real popover components (floating layers/popups) that support more complex content and interactions. Recommended for any scenario requiring interactive or rich popover content.

**Recommended usage:**

```tsx
import { StatefulPopover, Popover } from '@tigergraph/app-ui-lib/popover2';
```

- `StatefulPopover` is an uncontrolled component (manages its own open/close state internally).
- `Popover` is a controlled component (you control its open/close state via props).
- Please choose the appropriate popover version based on your use case to ensure consistent UI behavior.
