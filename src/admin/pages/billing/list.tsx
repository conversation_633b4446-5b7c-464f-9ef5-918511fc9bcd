import { columnOverrides } from '@/components/table';
import { TableContainer } from '@/lib/styled';
import { InlineBlock } from '@/pages/admin/user/styled';
import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import { Pagination } from '@tigergraph/app-ui-lib/pagination';
import { TableBuilder } from '@tigergraph/app-ui-lib/table';
import { Body1 } from '@tigergraph/app-ui-lib/typography';
import { TableBuilderColumn } from 'baseui/table-semantic';
import { ArrowUpIcon, ArrowDownIcon, FilterIcon } from 'lucide-react';
import React, { useState } from 'react';

import { LoadingIndicator } from '@/components/loading-indicator';
import { ErrorDisplay } from '@/components/error';
import { FilterBuilder } from '@/admin/components/FilterBuilder';
import { DatePicker } from '@tigergraph/app-ui-lib/datepicker';
import {
  CustomerBillingData,
  AdminBillingSort,
  FilterCondition,
  useAdminBilling,
  GroupingInterval,
} from '@/admin/hooks/useAdminBilling';
import IconButton from '@/components/IconButton';
import { RevenueItemsDropdown } from '@/admin/components/RevenueItemsDropdown';

const PAGE_SIZE = 50;

type SortDirection = 'asc' | 'desc';

interface SortState {
  field: AdminBillingSort;
  direction: SortDirection;
}

export default function BillingList() {
  const [css, theme] = useStyletron();
  const [page, setPage] = useState(1);
  const [filters, setFilters] = useState<FilterCondition[]>([]);
  const [sort, setSort] = useState<SortState>({ field: 'customer_infos.created_at', direction: 'desc' });
  const [groupingInterval, setGroupingInterval] = useState<GroupingInterval>('MONTH');
  const [dateRange, setDateRange] = useState<Date[]>([]);

  const formatDateToRFC3339 = (date: Date) => {
    const startOfDay = new Date(date.getFullYear(), date.getMonth(), date.getDate());
    return startOfDay.toISOString();
  };

  const { data, isLoading, error } = useAdminBilling({
    from: dateRange[0] ? formatDateToRFC3339(dateRange[0]) : undefined,
    to: dateRange[1] ? formatDateToRFC3339(dateRange[1]) : undefined,
    filters: filters.filter((f) => f.value !== ''),
    page,
    pageSize: PAGE_SIZE,
    sort: sort.field,
    sortDirection: sort.direction,
    groupingInterval,
  });

  const handleSort = (field: AdminBillingSort) => {
    setSort((prev) => ({
      field,
      direction: prev.field === field && prev.direction === 'asc' ? 'desc' : 'asc',
    }));
  };

  const renderSortHeader = (label: string, field: AdminBillingSort) => {
    return (
      <div className="flex items-center gap-1 cursor-pointer select-none" onClick={() => handleSort(field)}>
        {label}
        {sort.field === field && (sort.direction === 'asc' ? <ArrowUpIcon size={14} /> : <ArrowDownIcon size={14} />)}
      </div>
    );
  };

  const totalPages = data?.total ? Math.ceil(data.total / PAGE_SIZE) : 0;

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(amount);
  };

  return (
    <div className="space-y-6">
      {error ? <ErrorDisplay error={error as Error} /> : null}

      <div className="space-y-4">
        <div>
          <DatePicker
            value={dateRange}
            formatString={'yyyy/MM/dd'}
            placeholder="YYYY/MM/DD - YYYY/MM/DD"
            onChange={({ date }) => {
              setDateRange(date as Date[]);
              setPage(1);
            }}
            highlightedDate={new Date()}
            quickSelect={false}
            timeSelectStart={false}
            timeSelectEnd={false}
            range={true}
          />
        </div>

        <div className="flex gap-4 items-center">
          <FilterBuilder
            filters={filters}
            onChange={(newFilters) => {
              setFilters(newFilters);
              setPage(1);
            }}
            hideIncludeDeleted
            type="billing"
          />

          <div className="flex items-center gap-2">
            <label className="text-sm font-medium">Grouping:</label>
            <select
              className="border border-gray-300 rounded px-2 py-1 text-sm"
              value={groupingInterval}
              onChange={(e) => setGroupingInterval(e.target.value as GroupingInterval)}
            >
              <option value="HOUR">Hour</option>
              <option value="DAY">Day</option>
              <option value="WEEK">Week</option>
              <option value="MONTH">Month</option>
            </select>
          </div>
        </div>
      </div>

      <TableContainer $style={{ height: 'auto' }}>
        <TableBuilder data={data?.customer_billing_data || []}>
          <TableBuilderColumn header={renderSortHeader('Customer Name', 'customer_infos.customer_name')}>
            {(row: CustomerBillingData) => (
              <div className="group flex items-center gap-1">
                <span>{row.customer}</span>
                <IconButton
                  className="opacity-0 group-hover:opacity-100 transition-opacity"
                  onClick={(e: React.MouseEvent) => {
                    e.stopPropagation();
                    setFilters([{ field: 'customer_name', operator: 'eq', value: row.customer }]);
                    setPage(1);
                  }}
                >
                  <FilterIcon size={14} />
                </IconButton>
              </div>
            )}
          </TableBuilderColumn>

          <TableBuilderColumn header="Customer ID">
            {(row: CustomerBillingData) => (
              <div className="group flex items-center gap-1">
                <span className="font-mono text-sm">{row.customer_id}</span>
                <IconButton
                  className="opacity-0 group-hover:opacity-100 transition-opacity"
                  onClick={(e: React.MouseEvent) => {
                    e.stopPropagation();
                    setFilters([{ field: 'customer_id', operator: 'eq', value: row.customer_id }]);
                    setPage(1);
                  }}
                >
                  <FilterIcon size={14} />
                </IconButton>
              </div>
            )}
          </TableBuilderColumn>

          <TableBuilderColumn header={renderSortHeader('Email', 'customer_infos.emails')}>
            {(row: CustomerBillingData) => (
              <div className="group flex items-center gap-1">
                <span>{row.email}</span>
                <IconButton
                  className="opacity-0 group-hover:opacity-100 transition-opacity"
                  onClick={(e: React.MouseEvent) => {
                    e.stopPropagation();
                    setFilters([{ field: 'emails', operator: 'eq', value: row.email }]);
                    setPage(1);
                  }}
                >
                  <FilterIcon size={14} />
                </IconButton>
              </div>
            )}
          </TableBuilderColumn>

          <TableBuilderColumn header={renderSortHeader('Tier', 'quota.tier')}>
            {(row: CustomerBillingData) => (
              <div className="group flex items-center gap-1">
                <span
                  className={`px-2 py-1 rounded text-xs font-medium ${
                    row.tier === 'paid' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                  }`}
                >
                  {row.tier}
                </span>
                <IconButton
                  className="opacity-0 group-hover:opacity-100 transition-opacity"
                  onClick={(e: React.MouseEvent) => {
                    e.stopPropagation();
                    setFilters([{ field: 'tier', operator: 'eq', value: row.tier }]);
                    setPage(1);
                  }}
                >
                  <FilterIcon size={14} />
                </IconButton>
              </div>
            )}
          </TableBuilderColumn>

          <TableBuilderColumn header="Payment Method" overrides={columnOverrides}>
            {(row: CustomerBillingData) => (
              <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs font-medium">
                {row.default_payment}
              </span>
            )}
          </TableBuilderColumn>

          <TableBuilderColumn header="TigerGraph" overrides={columnOverrides}>
            {(row: CustomerBillingData) => (
              <span
                className={`px-2 py-1 rounded text-xs font-medium ${
                  row.tigergraph ? 'bg-purple-100 text-purple-800' : 'bg-gray-100 text-gray-800'
                }`}
              >
                {row.tigergraph ? 'Yes' : 'No'}
              </span>
            )}
          </TableBuilderColumn>

          <TableBuilderColumn header={renderSortHeader('Total Revenue', 'total_revenue')} overrides={columnOverrides}>
            {(row: CustomerBillingData) => (
              <div className="flex items-center justify-end gap-2">
                <span className="font-semibold text-green-600 font-mono">
                  {formatCurrency(row.customer_revenue?.total_revenue || 0)}
                </span>
                <RevenueItemsDropdown
                  customerName={row.customer}
                  revenueItems={row.customer_revenue?.revenue_items || []}
                  totalRevenue={row.customer_revenue?.total_revenue || 0}
                  groupingInterval={groupingInterval}
                />
              </div>
            )}
          </TableBuilderColumn>
        </TableBuilder>

        {isLoading && <LoadingIndicator />}

        <InlineBlock
          className={css({
            alignItems: 'center',
            justifyContent: 'flex-end',
            gap: '16px',
            marginTop: '24px',
          })}
        >
          <Body1 color={theme.colors.gray800}>
            {`${(page - 1) * PAGE_SIZE + 1} - ${Math.min(page * PAGE_SIZE, data?.total || 0)} of ${
              data?.total || 0
            } items`}
          </Body1>
          <Pagination totalPage={totalPages} pageNumber={page} setPageNumber={setPage} />
        </InlineBlock>
      </TableContainer>
    </div>
  );
}
