import { Card } from '@/admin/components/Card';
import { useAdminBillingAll, GroupingInterval } from '@/admin/hooks/useAdminBilling';
import { Chart } from '@tigergraph/app-ui-lib/charts';
import { LoadingIndicator } from '@/components/loading-indicator';
import { ErrorDisplay } from '@/components/error';
import { useState, useMemo } from 'react';
import { XCircleIcon } from 'lucide-react';
import { Button } from '@tigergraph/app-ui-lib/button';
import { DatePicker } from '@tigergraph/app-ui-lib/datepicker';

const formatDateToRFC3339 = (date: Date) => {
  const startOfDay = new Date(date.getFullYear(), date.getMonth(), date.getDate());
  return startOfDay.toISOString();
};

function pieChartConfig(
  title: string,
  data: { name: string; value: number }[],
  total: number,
  isRevenue: boolean = true
) {
  const option = {
    title: {
      text: isRevenue ? `${title} (Total: $${total.toFixed(2)})` : `${title} (Total: ${total} orgs)`,
      textStyle: {
        fontSize: 14,
        fontWeight: 'bold',
      },
      left: 'center',
      top: '0px',
    },
    tooltip: {
      trigger: 'item',
      formatter: (params: any) => {
        const percent = ((params.value / total) * 100).toFixed(1);
        if (isRevenue) {
          return `${params.name}: $${params.value.toFixed(2)} (${percent}%)`;
        } else {
          return `${params.name}: ${params.value} orgs (${percent}%)`;
        }
      },
    },
    legend: {
      orient: 'horizontal',
      left: 'center',
      top: '30px',
    },
    grid: {
      top: '70px',
    },
    series: [
      {
        name: 'Distribution',
        type: 'pie',
        radius: '60%',
        center: ['50%', '60%'],
        data: data,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)',
          },
        },
        label: {
          formatter: (params: any) => {
            const percent = ((params.value / total) * 100).toFixed(1);
            if (isRevenue) {
              return `${params.name}: $${params.value.toFixed(2)} (${percent}%)`;
            } else {
              return `${params.name}: ${params.value} (${percent}%)`;
            }
          },
        },
      },
    ],
  };
  return option;
}

function revenueHistogramConfig(data: { date: string; revenue: number }[], interval: GroupingInterval) {
  const formatDateLabel = (dateStr: string) => {
    const date = new Date(dateStr);

    switch (interval) {
      case 'HOUR':
        // Show hour format: "Jan 15 14:00"
        return (
          date.toLocaleDateString('en-US', {
            month: 'short',
            day: 'numeric',
          }) +
          ' ' +
          date.toLocaleTimeString('en-US', {
            hour: '2-digit',
            minute: '2-digit',
            hour12: false,
          })
        );

      case 'DAY':
        // Show day format: "Jan 15"
        return date.toLocaleDateString('en-US', {
          month: 'short',
          day: 'numeric',
        });

      case 'WEEK': {
        // Show week format: "Apr-w01"
        const month = date.toLocaleDateString('en-US', { month: 'short' });
        const startOfYear = new Date(date.getFullYear(), 0, 1);
        const weekNumber = Math.ceil(
          ((date.getTime() - startOfYear.getTime()) / 86400000 + startOfYear.getDay() + 1) / 7
        );
        return `${month}-w${weekNumber.toString().padStart(2, '0')}`;
      }

      case 'MONTH':
        // Show month format: "Jan 2024"
        return date.toLocaleDateString('en-US', {
          month: 'short',
          year: 'numeric',
        });

      default:
        return dateStr;
    }
  };

  return {
    title: {
      text: 'Revenue Trend',
      left: 'center',
      top: 20,
      textStyle: {
        fontSize: 14,
        fontWeight: 'bold',
      },
    },
    tooltip: {
      trigger: 'axis' as const,
      axisPointer: {
        type: 'shadow' as const,
      },
      formatter: function (params: any) {
        const item = params[0];
        return `${formatDateLabel(item.data.name)}: $${item.value.toFixed(2)}`;
      },
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '25%',
      top: '70px',
      containLabel: true,
    },
    dataZoom: [
      {
        type: 'slider',
        show: true,
        xAxisIndex: [0],
        start: 0,
        end: 100,
        height: 30,
        bottom: 60,
        borderColor: 'transparent',
        backgroundColor: '#e2e8f0',
        fillerColor: 'rgba(37, 99, 235, 0.1)',
        handleStyle: {
          color: '#2563eb',
        },
        moveHandleStyle: {
          color: '#2563eb',
        },
        selectedDataBackground: {
          lineStyle: {
            color: '#2563eb',
          },
          areaStyle: {
            color: '#2563eb',
          },
        },
        emphasis: {
          handleStyle: {
            color: '#1e40af',
          },
        },
      },
      {
        type: 'inside',
        xAxisIndex: [0],
        start: 0,
        end: 100,
        zoomOnMouseWheel: 'shift',
        moveOnMouseMove: true,
      },
    ],
    xAxis: {
      type: 'category' as const,
      data: data.map((item) => formatDateLabel(item.date)),
      axisLabel: {
        rotate: 45,
      },
      boundaryGap: true,
    },
    yAxis: {
      type: 'value' as const,
      splitLine: {
        lineStyle: {
          type: 'dashed' as const,
        },
      },
      axisLabel: {
        formatter: (value: number) => `$${value.toFixed(0)}`,
      },
    },
    series: [
      {
        name: 'Revenue',
        type: 'bar',
        data: data.map((item) => ({
          value: item.revenue,
          name: item.date,
        })),
        barMaxWidth: 30,
        itemStyle: {
          color: '#10b981',
          borderRadius: [4, 4, 0, 0],
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowColor: 'rgba(0,0,0,0.3)',
          },
        },
        label: {
          show: false,
        },
      },
    ],
  };
}

const style = {
  backgroundColor: 'white',
  paddingTop: '1rem',
  height: '500px',
};

const histogramStyle = {
  backgroundColor: 'white',
  paddingTop: '1rem',
  height: '600px',
};

export default function Statistics() {
  const [dateRange, setDateRange] = useState<Date[]>([]);
  const [interval, setInterval] = useState<GroupingInterval>('MONTH');

  // Fetch all billing data at once
  const {
    data: billingData,
    isLoading,
    error,
  } = useAdminBillingAll({
    from: dateRange.length === 2 ? formatDateToRFC3339(dateRange[0]) : undefined,
    to: dateRange.length === 2 ? formatDateToRFC3339(dateRange[1]) : undefined,
    groupingInterval: interval,
  });

  // Frontend data aggregation
  const aggregatedData = useMemo(() => {
    if (!billingData?.customer_billing_data?.length) return null;

    const customers = billingData.customer_billing_data;

    // Total organization count
    const totalOrgs = customers.length;

    // Total revenue
    const totalRevenue = customers.reduce((sum, customer) => sum + (customer.customer_revenue?.total_revenue || 0), 0);

    // Payment Method distribution
    const paymentMethodRevenue: Record<string, number> = {};
    customers.forEach((customer) => {
      const revenue = customer.customer_revenue?.total_revenue || 0;
      const method = customer.default_payment || 'Unknown';
      paymentMethodRevenue[method] = (paymentMethodRevenue[method] || 0) + revenue;
    });

    // Tier distribution
    const tierRevenue: Record<string, number> = {};
    customers.forEach((customer) => {
      const revenue = customer.customer_revenue?.total_revenue || 0;
      const tier = customer.tier || 'Unknown';
      tierRevenue[tier] = (tierRevenue[tier] || 0) + revenue;
    });

    // Revenue trend (aggregated from revenue_items)
    const revenueByDate: Record<string, number> = {};
    customers.forEach((customer) => {
      customer.customer_revenue?.revenue_items?.forEach((item) => {
        revenueByDate[item.date] = (revenueByDate[item.date] || 0) + item.revenue;
      });
    });

    const revenueTrend = Object.entries(revenueByDate)
      .map(([date, revenue]) => ({ date, revenue }))
      .sort((a, b) => a.date.localeCompare(b.date));

    // Organization count grouped by revenue range
    const revenueRangeCount = {
      'Under $100': 0,
      '$100 - $1,000': 0,
      '$1,000 - $10,000': 0,
      'Over $10,000': 0,
    };

    customers.forEach((customer) => {
      const revenue = customer.customer_revenue?.total_revenue || 0;
      if (revenue < 100) {
        revenueRangeCount['Under $100']++;
      } else if (revenue < 1000) {
        revenueRangeCount['$100 - $1,000']++;
      } else if (revenue < 10000) {
        revenueRangeCount['$1,000 - $10,000']++;
      } else {
        revenueRangeCount['Over $10,000']++;
      }
    });

    return {
      totalOrgs,
      totalRevenue,
      paymentMethodRevenue,
      tierRevenue,
      revenueTrend,
      revenueRangeCount,
    };
  }, [billingData]);

  if (error) {
    return <ErrorDisplay error={error as Error} />;
  }

  if (isLoading) {
    return (
      <div className="w-full h-full flex items-center justify-center">
        <LoadingIndicator />
      </div>
    );
  }

  if (!aggregatedData) {
    return (
      <div className="w-full h-full flex items-center justify-center">
        <div className="text-gray-500">No data available</div>
      </div>
    );
  }

  // Prepare Payment Method chart data
  const paymentMethodData = Object.entries(aggregatedData.paymentMethodRevenue).map(([method, revenue]) => ({
    name: method,
    value: revenue,
  }));

  // Prepare Tier chart data
  const tierData = Object.entries(aggregatedData.tierRevenue).map(([tier, revenue]) => ({
    name: tier.charAt(0).toUpperCase() + tier.slice(1),
    value: revenue,
  }));

  // Prepare revenue range organization count chart data
  const revenueRangeData = Object.entries(aggregatedData.revenueRangeCount).map(([range, count]) => ({
    name: range,
    value: count,
  }));

  const clearFilter = () => {
    setDateRange([]);
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center mb-4">
        <h1 className="text-2xl font-bold">Billing Statistics</h1>
        <div className="flex gap-2">
          {dateRange.length > 0 && (
            <Button kind="secondary" onClick={clearFilter} startEnhancer={<XCircleIcon size={16} />}>
              Clear Filters
            </Button>
          )}
        </div>
      </div>

      {/* Filters and controls */}
      <div className="bg-white p-4 rounded-lg space-y-4">
        <div className="flex gap-4 items-center">
          <DatePicker
            value={dateRange}
            formatString={'yyyy/MM/dd'}
            placeholder="YYYY/MM/DD - YYYY/MM/DD"
            onChange={({ date }) => {
              setDateRange(date as Date[]);
            }}
            highlightedDate={new Date()}
            quickSelect={false}
            timeSelectStart={false}
            timeSelectEnd={false}
            range={true}
          />

          <div className="flex items-center gap-2">
            <label className="text-sm font-medium">Interval:</label>
            <select
              className="border border-gray-300 rounded px-2 py-1 text-sm"
              value={interval}
              onChange={(e) => setInterval(e.target.value as GroupingInterval)}
            >
              <option value="HOUR">Hour</option>
              <option value="DAY">Day</option>
              <option value="WEEK">Week</option>
              <option value="MONTH">Month</option>
            </select>
          </div>
        </div>
      </div>

      {/* Summary cards */}
      <div className="grid grid-cols-2 gap-4">
        <div className="p-4 bg-white">
          <Card title="Total Organizations" number={aggregatedData.totalOrgs} />
        </div>
        <div className="p-4 bg-white">
          <Card title="Total Revenue" number={`$${aggregatedData.totalRevenue.toFixed(2)}`} />
        </div>
      </div>

      {/* Chart displays */}
      <div className="grid grid-cols-3 gap-4">
        <div className="bg-white p-4">
          <Chart
            option={pieChartConfig('Revenue by Payment Method', paymentMethodData, aggregatedData.totalRevenue, true)}
            style={style}
          />
        </div>

        <div className="bg-white p-4">
          <Chart
            option={pieChartConfig('Revenue by Tier', tierData, aggregatedData.totalRevenue, true)}
            style={style}
          />
        </div>

        <div className="bg-white p-4">
          <Chart
            option={pieChartConfig('Organizations by Revenue Range', revenueRangeData, aggregatedData.totalOrgs, false)}
            style={style}
          />
        </div>
      </div>

      {/* Revenue trend chart */}
      {aggregatedData.revenueTrend.length > 0 && (
        <div className="bg-white p-4 rounded-lg shadow">
          <Chart option={revenueHistogramConfig(aggregatedData.revenueTrend, interval)} style={histogramStyle} />
        </div>
      )}
    </div>
  );
}
