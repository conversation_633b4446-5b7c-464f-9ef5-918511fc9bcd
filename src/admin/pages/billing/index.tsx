import Statistics from '@/admin/pages/billing/statistics';
import BillingList from '@/admin/pages/billing/list';
import { Tab, Tabs } from '@/components/Tab';
import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import { useSearchParams } from 'react-router-dom';

export default function Billing() {
  const [, theme] = useStyletron();
  const [searchParams, setSearchParams] = useSearchParams();
  const tabOverrides = { TabPanel: { style: { backgroundColor: theme.colors['background.secondary'] } } };

  return (
    <div className="pt-4 h-full">
      <Tabs
        activeKey={searchParams.get('tab') || 'statistics'}
        onChange={({ activeKey }) => {
          setSearchParams({ tab: activeKey as string });
        }}
        activateOnFocus
      >
        <Tab title="Statistics" key="statistics" overrides={tabOverrides}>
          <Statistics />
        </Tab>
        <Tab title="Data" key="data">
          <BillingList />
        </Tab>
      </Tabs>
    </div>
  );
}
