import { format } from 'date-fns';
import { parseDate } from '@/lib/date';
import { useAdminOrgDetail } from '@/admin/hooks/useAdminOrgDetail';
import { LoadingIndicator } from '@/components/loading-indicator';
import { ErrorDisplay } from '@/components/error';
import { useState } from 'react';
import EditOrgDrawer from '@/admin/pages/org/editDrawer';
import { Button } from '@tigergraph/app-ui-lib/button';
import { MdEdit, MdDelete, MdAdd } from 'react-icons/md';
import React from 'react';
import { DatabaseList } from '@/admin/components/DatabaseList';
import { WorkgroupList } from '@/admin/components/WorkgroupList';
import { UserList } from '@/admin/components/UserList';
import { WorkspaceList } from '@/admin/components/WorkspaceList';
import { useParams, useNavigate, useSearchParams } from 'react-router-dom';
import { ArrowLeftIcon } from 'lucide-react';
import { Tab, Tabs } from '@/components/Tab';
import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import { AmberfloProvider, SDKTheme } from '@amberflo/uikit';
import { axiosController } from '@/lib/network';
import { useQuery } from 'react-query';
import { AxiosError } from 'axios';
import Invoices from '@/pages/admin/bill/Invoices';
import { useAddOrgFeatureFlag, useDeleteOrgFeatureFlag, FeatureFlag } from '@/admin/hooks/useAdminMutateOrgFeatureFlag';
import { Modal } from '@tigergraph/app-ui-lib/modal';

const amberfloTheme: SDKTheme = {
  barChartColors: ['#FCAE69', '#FDD7B4'],
};

export default function OrgDetail() {
  const { orgId } = useParams<{ orgId: string }>();
  const navigate = useNavigate();
  const [css, theme] = useStyletron();
  const [searchParams, setSearchParams] = useSearchParams();

  const {
    data: detail,
    isLoading,
    error,
    refetch: refetchOrgDetail,
  } = useAdminOrgDetail({
    orgID: orgId!,
    enabled: !!orgId,
  });

  const [isEditDrawerOpen, setIsEditDrawerOpen] = useState(false);
  const [isDatabaseListOpen, setIsDatabaseListOpen] = useState(false);
  const [isWorkgroupListOpen, setIsWorkgroupListOpen] = useState(false);
  const [isUserListOpen, setIsUserListOpen] = useState(false);
  const [isWorkspaceListOpen, setIsWorkspaceListOpen] = useState(false);
  const [isAddFlagOpen, setIsAddFlagOpen] = useState(false);
  const [newFlagName, setNewFlagName] = useState('');
  const [newFlagEnabled, setNewFlagEnabled] = useState(true);
  const [flagLoading, setFlagLoading] = useState(false);

  const { mutate: addFlag } = useAddOrgFeatureFlag(orgId!, {
    onSuccess: () => {
      setIsAddFlagOpen(false);
      setNewFlagName('');
      setNewFlagEnabled(true);
      setFlagLoading(false);
    },
    onError: () => setFlagLoading(false),
  }) as { mutate: (variables: FeatureFlag[], options?: any) => void };
  const { mutate: deleteFlag } = useDeleteOrgFeatureFlag(orgId!, {
    onSuccess: () => setFlagLoading(false),
    onError: () => setFlagLoading(false),
  }) as { mutate: (variables: FeatureFlag[], options?: any) => void };

  const refetchDetail = () => {
    refetchOrgDetail();
  };

  const handleAddFlag = () => {
    if (!newFlagName) return;
    setFlagLoading(true);
    addFlag([{ feature_name: newFlagName, enabled: newFlagEnabled }], {
      onSuccess: () => {
        setIsAddFlagOpen(false);
        setNewFlagName('');
        setNewFlagEnabled(true);
        setFlagLoading(false);
        refetchDetail();
      },
      onError: () => setFlagLoading(false),
    });
  };

  const handleDeleteFlag = (flag: FeatureFlag) => {
    setFlagLoading(true);
    deleteFlag([{ feature_name: flag.feature_name, enabled: flag.enabled }], {
      onSuccess: () => {
        setFlagLoading(false);
        refetchDetail();
      },
      onError: () => setFlagLoading(false),
    });
  };

  const {
    data: sessionToken,
    isLoading: isLoadingSessionToken,
    isError: isSessionTokenError,
    error: sessionTokenError,
  } = useQuery<string, AxiosError>(
    ['sessionToken', orgId],
    async () => {
      const response = await axiosController.post(`/admin/orgs/${orgId}/billing/session`);
      return response.data.Result;
    },
    {
      enabled: !!orgId,
    }
  );

  const handleWorkspaceCountClick = () => {
    setIsWorkspaceListOpen(true);
  };

  const renderField = (
    label: string,
    value: string | number | boolean | null | undefined,
    extraContent?: React.ReactNode,
    onClick?: () => void
  ) => (
    <div className="grid grid-cols-[280px_1fr] gap-6 py-1 border-b border-gray-100">
      <div className="text-gray-600 text-sm whitespace-nowrap">{label}</div>
      <div className="break-all text-sm pr-4 flex items-center gap-2">
        <span className={onClick ? 'text-blue-600 cursor-pointer hover:underline' : undefined} onClick={onClick}>
          {value?.toString() || '-'}
        </span>
        {extraContent}
      </div>
    </div>
  );

  const renderSection = (
    title: string,
    fields: { label: string; value: any; extraContent?: React.ReactNode; onClick?: () => void }[]
  ) => (
    <div className="space-y-2">
      <div className="text-sm font-medium text-gray-700 mb-2">{title}</div>
      <div className="space-y-1">
        {fields.map((field, index) => renderField(field.label, field.value, field.extraContent, field.onClick))}
      </div>
    </div>
  );

  if (!orgId) {
    return <div>No organization ID provided</div>;
  }

  return (
    <>
      <div className="p-6 h-full flex flex-col">
        <div className="flex items-center mb-6 gap-4">
          <Button
            onClick={() => navigate(-1)}
            kind="secondary"
            size="compact"
            startEnhancer={<ArrowLeftIcon size={16} />}
          >
            Back
          </Button>
          <h2>Organization Details</h2>
        </div>

        <Tabs
          activeKey={searchParams.get('tab') || 'details'}
          onChange={({ activeKey }) => {
            const currentParams = new URLSearchParams(searchParams);
            currentParams.set('tab', activeKey as string);
            // Clean up any tab params
            currentParams.delete('billingTab');
            setSearchParams(currentParams, {
              replace: true,
            });
          }}
          activateOnFocus
        >
          <Tab title="Details" key="details">
            {error ? (
              <ErrorDisplay error={error as Error} />
            ) : isLoading ? (
              <LoadingIndicator />
            ) : detail ? (
              <div className="grid grid-cols-2 gap-x-8">
                {/* Left Column */}
                <div className="space-y-6">
                  {renderSection('Basic Information', [
                    { label: 'Organization ID', value: detail.org_id },
                    { label: 'Organization Name', value: detail.org_name },
                    { label: 'Display Name', value: detail.org_display_name },
                    { label: 'Creator', value: detail.creator },
                    { label: 'Created At', value: format(parseDate(detail.create_time), 'yyyy-MM-dd HH:mm:ss') },
                  ])}

                  {renderSection('Statistics', [
                    { label: 'Workspace Count', value: detail.workspace_count, onClick: handleWorkspaceCountClick },
                    {
                      label: 'Workgroup Count',
                      value: detail.workgroup_count,
                      onClick: () => setIsWorkgroupListOpen(true),
                    },
                    {
                      label: 'Database Count',
                      value: detail.tg_database_count,
                      onClick: () => setIsDatabaseListOpen(true),
                    },
                    { label: 'User Count', value: detail.user_count, onClick: () => setIsUserListOpen(true) },
                  ])}

                  {/* Payment Methods */}
                  {detail.payment_methods?.credit_cards && detail.payment_methods.credit_cards.length > 0 && (
                    <div className="space-y-2">
                      <div className="text-sm font-medium text-gray-700 mb-2">Payment Methods</div>
                      <div className="space-y-2">
                        {detail.payment_methods.credit_cards.map((card) => (
                          <div key={card.card_id} className="flex items-center gap-2">
                            <span className="text-sm">
                              {card.brand} **** {card.last_4_digits} ({card.name_on_card})
                            </span>
                            {card.card_id === detail.payment_methods.default_card && (
                              <span className="text-xs bg-blue-100 text-blue-800 px-2 py-0.5 rounded">Default</span>
                            )}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Logo */}
                  <div className="space-y-2">
                    <div className="text-sm font-medium text-gray-700 mb-2">Logo</div>
                    <div>
                      {detail.logo_url ? (
                        <div className="space-y-2">
                          <img
                            src={detail.logo_url}
                            alt={`${detail.org_name} logo`}
                            className="max-w-[200px] max-h-[200px] object-contain border border-gray-200 rounded-lg"
                          />
                          <div className="text-sm text-gray-500 break-all">{detail.logo_url}</div>
                        </div>
                      ) : (
                        <div className="text-gray-500">-</div>
                      )}
                    </div>
                  </div>
                </div>

                {/* Right Column */}
                <div className="space-y-6">
                  <div className="space-y-2">
                    <div className="text-sm font-medium text-gray-700 mb-2">Quota Information</div>
                    <div className="space-y-1">
                      {renderField(
                        'Tier',
                        detail.quota?.tier,
                        <Button
                          onClick={() => setIsEditDrawerOpen(true)}
                          size="compact"
                          kind="secondary"
                          startEnhancer={<MdEdit size={14} />}
                        >
                          Edit
                        </Button>
                      )}
                      {renderField('Max Workspace Type', detail.quota?.max_workspace_type)}
                      {renderField('Max Workspace Memory', detail.quota?.max_workspace_memory)}
                      {renderField('Read/Write Workspace Count Limit', detail.quota?.rw_workspace_count_limit)}
                      {renderField('Read-Only Workspace Count Limit', detail.quota?.ro_workspace_count_limit)}
                      {renderField('Memory Limit', detail.quota?.memory_limit)}
                      {renderField(
                        'Workspace Auto Backup Count Limit',
                        detail.quota?.workspace_auto_backup_count_limit
                      )}
                      {renderField(
                        'Workspace Auto Backup Retention In Days',
                        detail.quota?.workspace_auto_backup_retention_in_days
                      )}
                      {renderField(
                        'Workspace Manual Backup Count Limit',
                        detail.quota?.workspace_manual_backup_count_limit
                      )}
                      {renderField('Read/Write Workspace Count Usage', detail.quota?.rw_workspace_count_usage)}
                      {renderField('Read-Only Workspace Count Usage', detail.quota?.ro_workspace_count_usage)}
                      {renderField('Memory Usage', detail.quota?.memory_usage_in_bytes)}
                    </div>
                  </div>

                  {/* Feature Flags */}
                  <div className="space-y-2">
                    <div className="flex items-center justify-between mb-2">
                      <div className="text-sm font-medium text-gray-700">Feature Flags</div>
                      <Button
                        size="compact"
                        kind="secondary"
                        startEnhancer={<MdAdd size={16} />}
                        onClick={() => setIsAddFlagOpen(true)}
                      >
                        Add
                      </Button>
                    </div>
                    <div className="space-y-1">
                      {detail?.feature_flag?.length ? (
                        detail.feature_flag.map((flag) => (
                          <div
                            key={flag.feature_name}
                            className="flex items-center gap-2 border-b border-gray-100 py-1"
                          >
                            <span className="text-sm w-40">{flag.feature_name}</span>
                            <span className="text-xs px-2 py-0.5 rounded bg-gray-100 text-gray-700">
                              {flag.enabled ? 'true' : 'false'}
                            </span>
                            <Button
                              size="compact"
                              kind="text"
                              startEnhancer={<MdDelete size={14} />}
                              onClick={() => handleDeleteFlag(flag)}
                              disabled={flagLoading}
                            >
                              Delete
                            </Button>
                          </div>
                        ))
                      ) : (
                        <div className="text-gray-400 text-sm">暂无 Feature Flag</div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ) : null}
          </Tab>
          <Tab title="Invoices" key="invoices">
            {error ? (
              <ErrorDisplay error={(error as Error) || new Error('Organization data not available')} />
            ) : isLoading || isLoadingSessionToken ? (
              <LoadingIndicator />
            ) : isSessionTokenError ? (
              <ErrorDisplay error={sessionTokenError as Error} label="Server error:" />
            ) : (
              <AmberfloProvider session={sessionToken!} theme={amberfloTheme}>
                <Invoices />
              </AmberfloProvider>
            )}
          </Tab>
        </Tabs>
      </div>

      <EditOrgDrawer isOpen={isEditDrawerOpen} onClose={() => setIsEditDrawerOpen(false)} orgID={orgId} />

      {detail && (
        <>
          <DatabaseList detail={detail} isOpen={isDatabaseListOpen} onClose={() => setIsDatabaseListOpen(false)} />

          <WorkgroupList detail={detail} isOpen={isWorkgroupListOpen} onClose={() => setIsWorkgroupListOpen(false)} />

          <UserList detail={detail} isOpen={isUserListOpen} onClose={() => setIsUserListOpen(false)} />

          <WorkspaceList detail={detail} isOpen={isWorkspaceListOpen} onClose={() => setIsWorkspaceListOpen(false)} />
        </>
      )}
      {/* Add Feature Flag Modal */}
      {isAddFlagOpen && (
        <Modal isOpen={isAddFlagOpen} onClose={() => setIsAddFlagOpen(false)} size="default">
          <div className="p-6 space-y-4">
            <div className="text-lg font-medium">Add Feature Flag</div>
            <div className="space-y-2">
              <div>
                <label className="block text-sm mb-1">Feature Name</label>
                <input
                  className="border rounded px-2 py-1 w-full"
                  value={newFlagName}
                  onChange={(e) => setNewFlagName(e.target.value)}
                  placeholder="feature_xxx"
                  disabled={flagLoading}
                />
              </div>
              <div>
                <label className="block text-sm mb-1">Enabled</label>
                <select
                  className="border rounded px-2 py-1 w-full"
                  value={newFlagEnabled ? 'true' : 'false'}
                  onChange={(e) => setNewFlagEnabled(e.target.value === 'true')}
                  disabled={flagLoading}
                >
                  <option value="true">true</option>
                  <option value="false">false</option>
                </select>
              </div>
            </div>
            <div className="flex justify-end gap-2">
              <Button kind="secondary" size="compact" onClick={() => setIsAddFlagOpen(false)} disabled={flagLoading}>
                Cancel
              </Button>
              <Button kind="primary" size="compact" onClick={handleAddFlag} disabled={flagLoading || !newFlagName}>
                Confirm
              </Button>
            </div>
          </div>
        </Modal>
      )}
    </>
  );
}
