import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import { memo, useEffect, useState } from 'react';
import { LoadingIndicator } from '@/components/loading-indicator';
import { useTheme } from '@/contexts/themeContext';
import { ID_TOKEN_KEY } from '@/contexts/workspaceContext';

function getGrafanaURL(themeType: 'light' | 'dark') {
  let grafanaURL: string = import.meta.env.VITE_GRAFANA_ADMIN_URL;
  const searchParams = new URLSearchParams();
  let queryParams: Record<string, string> = {
    // 'var-orgId': orgId,
    // refresh: '30s',
    // 'var-ServiceName': 'All',
    // 'var-pod': 'All',
    // 'var-workspaceId': 'All',
    // 'var-workgroupId': 'All',
    auth_token: sessionStorage.getItem(ID_TOKEN_KEY)!,
    theme: themeType,
  };
  Object.keys(queryParams).forEach((key) => {
    searchParams.set(key, queryParams[key]);
  });
  grafanaURL += `?${searchParams.toString()}&&kiosk`;
  return grafanaURL;
}

const GrafanaIframe = memo(({ url }: { url: string }) => {
  const [css] = useStyletron();
  return <iframe title="dashboard" src={url} className={css({ width: '100%', height: '100%' })} />;
});

const AccessDescription = () => {
  const [css, theme] = useStyletron();

  return (
    <div
      className={css({
        margin: '8px',
        padding: '8px',
        marginBottom: '16px',
        backgroundColor: theme.colors['background.secondary'],
        border: `1px solid ${theme.colors['border.primary']}`,
        borderRadius: '2px',
        fontSize: '14px',
        lineHeight: '1.5',
      })}
    >
      <div>This Grafana dashboard can only be accessed from:</div>
      <ul className={css({ margin: '2px 0 0 0', paddingLeft: '20px' })}>
        <li>1. RWC or SV4 (VPN connection)</li>
        <li>2. Changsha Office or Santa Clara Office (office network)</li>
      </ul>
    </div>
  );
};

export default function Dashboards() {
  const [css, theme] = useStyletron();
  const { themeType } = useTheme();
  const [showLoading, setShowLoading] = useState(false);

  useEffect(() => {
    if (!customElements) {
      return;
    }
    setShowLoading(true);
    const handleIframeMessage = (event: MessageEvent<{ type: string }>) => {
      if (event.data.type === 'iframe-ready') {
        setShowLoading(false);
      }
    };
    window.addEventListener('message', handleIframeMessage, false);

    return () => {
      window.removeEventListener('message', handleIframeMessage);
    };
  }, []);

  return (
    <div
      className={css({
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        position: 'relative',
      })}
    >
      <AccessDescription />
      <div className={css({ width: '100%', flexGrow: 1, position: 'relative' })}>
        {showLoading && (
          <div
            className={css({
              position: 'absolute',
              background: `${theme.colors['background.primary']}`,
              width: '100%',
              height: '100%',
            })}
          >
            <LoadingIndicator />
          </div>
        )}
        <GrafanaIframe url={getGrafanaURL(themeType)} />
      </div>
    </div>
  );
}
