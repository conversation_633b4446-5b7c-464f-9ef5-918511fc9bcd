import { Modal } from '@tigergraph/app-ui-lib/modal';
import { AdminWorkspace } from '../hooks/useAdminWorkspaces';
import { useAdminWorkspaceDetail } from '../hooks/useAdminWorkspaceDetail';
import { LoadingIndicator } from '@/components/loading-indicator';
import { ErrorDisplay } from '@/components/error';
import { format } from 'date-fns';
import { parseDate } from '@/lib/date';
import { formatBytes } from '@/utils/format';
import { Button } from '@tigergraph/app-ui-lib/button';
import { Trash2Icon, PauseIcon, PlayIcon, EditIcon, ChevronDownIcon, ChevronRightIcon } from 'lucide-react';
import { useAdminDashboardPermission } from '../hooks/useAdminDashboardPermission';
import { AxiosError } from 'axios';
import { useState } from 'react';
import { ArrowUp } from 'baseui/icon';
import { useMutationUpdateWorkspace } from '@/pages/workgroup/hook';
import toast from 'react-hot-toast';
import { getErrorMessage } from '@/utils/utils';
import { UpdateWorkspaceRequest } from '@/pages/workgroup/type';

interface WorkspaceDetailProps {
  workspace: AdminWorkspace;
  isOpen: boolean;
  onClose: () => void;
  onAction: (action: 'Pause' | 'Resume' | 'Terminate' | 'Edit') => void;
}

interface TreeNodeProps {
  label: string;
  value: string | number | null | undefined;
  items?: {
    label: string;
    value: string | number | null | undefined;
    items?: { label: string; value: string | number | null | undefined }[];
  }[];
}

function TreeNode({ label, value, items }: TreeNodeProps) {
  const [isExpanded, setIsExpanded] = useState(true);
  const hasChildren = items && items.length > 0;

  return (
    <div className="ml-2">
      <div className="flex items-start gap-2 py-1 border-b border-gray-100">
        {hasChildren ? (
          <button onClick={() => setIsExpanded(!isExpanded)} className="p-1 mt-0.5">
            {isExpanded ? <ChevronDownIcon size={16} /> : <ChevronRightIcon size={16} />}
          </button>
        ) : (
          <div className="w-[32px]" />
        )}
        <div className="grid grid-cols-[1fr_120px] gap-4 flex-1">
          <div className="text-gray-600 text-sm break-all">{label}</div>
          <div className="text-sm text-right">{value?.toString() || '-'}</div>
        </div>
      </div>
      {hasChildren && isExpanded && (
        <div className="ml-4">
          {items.map((item, index) => (
            <TreeNode key={index} {...item} />
          ))}
        </div>
      )}
    </div>
  );
}

export function WorkspaceDetail({ workspace, isOpen, onClose, onAction }: WorkspaceDetailProps) {
  const {
    data: detail,
    isLoading,
    error,
  } = useAdminWorkspaceDetail({
    orgID: workspace.org_id,
    workgroupID: workspace.workgroup_id,
    workspaceID: workspace.workspace_id,
  });

  const { permission } = useAdminDashboardPermission();

  const renderField = (label: string, value: string | number | boolean | null | undefined) => (
    <div className="grid grid-cols-[180px_1fr] gap-6 py-1 border-b border-gray-100">
      <div className="text-gray-600 text-sm whitespace-nowrap">{label}</div>
      <div className="break-all text-sm pr-4">{value?.toString() || '-'}</div>
    </div>
  );

  const leftColumnFields = [
    { label: 'ID', value: detail?.workspace_id },
    { label: 'Name', value: detail?.name },
    { label: 'Creator', value: detail?.creator },
    {
      label: 'Created At',
      value: detail?.created_at ? format(parseDate(detail.created_at), 'yyyy-MM-dd HH:mm:ss') : '-',
    },
    {
      label: 'Last Modified At',
      value: detail?.last_modified_time ? format(parseDate(detail.last_modified_time), 'yyyy-MM-dd HH:mm:ss') : '-',
    },
    { label: 'Type', value: detail?.workspace_type?.typeName },
    { label: 'Organization', value: detail ? `${workspace.org_name} (${workspace.org_id})` : '-' },
    { label: 'Workgroup ID', value: detail?.workgroup_id },
    { label: 'Workgroup Name', value: detail?.workgroup_name || '-' },
    { label: 'Database ID', value: detail?.database_id },
    { label: 'Database Name', value: detail?.database_name || '-' },
    { label: 'Region', value: detail?.region },
    { label: 'Status', value: detail?.status || '-' },
    { label: 'Cloud Provider ID', value: detail?.cloud_provider_id || '-' },
    { label: 'Cloud Provider Name', value: detail?.cloud_provider_name || '-' },
    { label: 'Cloud Provider ShortID', value: detail?.cloud_provider_short_id || '-' },
    { label: 'Solution Catalog ID', value: detail?.solution_catalog_id || '-' },
    { label: 'Update Strategy', value: detail?.update_strategy || '-' },
  ];

  const rightColumnFields = [
    { label: 'Condition Type', value: detail?.condition_type || '-' },
    { label: 'Version', value: detail?.tg_version },
    { label: 'Platform', value: detail?.platform || '-' },
    { label: 'Topology Size', value: formatBytes(detail?.graph_topology_size_bytes || 0) },
    { label: 'CPU', value: detail?.workspace_type?.cpu },
    { label: 'Memory', value: detail?.workspace_type?.memory },
    { label: 'Partition', value: detail?.workspace_type?.partition },
    { label: 'HA', value: detail?.workspace_type?.ha },
    { label: 'Auto Suspend', value: detail?.auto_stop_minutes ? (detail.auto_stop_minutes > 0 ? 'Yes' : 'No') : '-' },
    { label: 'Auto Resume', value: detail?.enable_auto_start ? 'Yes' : 'No' },
    {
      label: 'Enable Topology Force Update',
      value: detail?.agent_config?.enable_topology_force_update?.toString() || '-',
    },
    {
      label: 'Heartbeat Interval',
      value: detail?.agent_config?.heartbeat_interval_seconds
        ? `${detail.agent_config.heartbeat_interval_seconds}s`
        : '-',
    },
    {
      label: 'Topology Update Interval',
      value: detail?.agent_config?.topology_update_interval_seconds
        ? `${detail.agent_config.topology_update_interval_seconds}s`
        : '-',
    },
    {
      label: 'Catalog Activity Update Interval',
      value: detail?.agent_config?.catalog_activity_update_interval_seconds
        ? `${detail.agent_config.catalog_activity_update_interval_seconds}s`
        : '-',
    },
    {
      label: 'Query Activity Update Interval',
      value: detail?.agent_config?.query_activity_update_interval_seconds
        ? `${detail.agent_config.query_activity_update_interval_seconds}s`
        : '-',
    },
    { label: 'Refresh Status', value: detail?.refresh_status || '-' },
    { label: 'Refresh Message', value: detail?.refresh_message || '-' },
  ];

  const folderStatisticsTree = {
    label: 'Folder Statistics',
    value: detail?.folder_statistics?.total_size ? formatBytes(detail.folder_statistics.total_size) : '-',
    items: [
      {
        label: 'Milvus Size',
        value: detail?.folder_statistics?.milvus_size ? formatBytes(detail.folder_statistics.milvus_size) : '-',
      },
      {
        label: 'Backup Size',
        value: detail?.folder_statistics?.backup_size ? formatBytes(detail.folder_statistics.backup_size) : '-',
      },
      {
        label: 'Log Size',
        value: detail?.folder_statistics?.log_size ? formatBytes(detail.folder_statistics.log_size) : '-',
      },
      {
        label: 'Billed Size',
        value: detail?.folder_statistics?.billed_size ? formatBytes(detail.folder_statistics.billed_size) : '-',
      },
      {
        label: 'Workspaces',
        value: `${detail?.folder_statistics?.workspaces?.length || 0} workspaces`,
        items:
          detail?.folder_statistics?.workspaces?.map((ws) => ({
            label: ws.workspace_id,
            value: formatBytes(ws.size),
          })) || [],
      },
    ],
  };

  const updateWorkSpaceMutation = useMutationUpdateWorkspace();

  const onUpdate = () => {
    const updateRequest: UpdateWorkspaceRequest = {
      update_strategy: 'update_strategy_always',
    };

    const promise = updateWorkSpaceMutation.mutateAsync({
      group_id: workspace.workgroup_id,
      space_id: workspace.workspace_id,
      org_id: workspace.org_id,
      data: updateRequest,
    });

    toast.promise(
      promise,
      {
        loading: 'Upgrading workspace',
        success: (data) => 'Workspace Upgrading request accepted.',
        error: (err) => `${getErrorMessage(err)}`,
      },
      {}
    );
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      size="auto"
      overrides={{
        Close: {
          style: {
            top: '24px',
            right: '24px',
          },
        },
      }}
    >
      <div className="p-6 w-[1200px] max-h-[800px] overflow-y-auto">
        <div className="flex justify-between items-center mb-6 pr-8">
          <h2 className="text-xl font-semibold">Workspace Details</h2>
          {permission?.updateWorkspace && (
            <div className="flex gap-3">
              <Button kind="secondary" onClick={() => onAction('Edit')} startEnhancer={<EditIcon size={16} />}>
                Edit
              </Button>
              <Button kind="secondary" onClick={() => onAction('Terminate')} startEnhancer={<Trash2Icon size={16} />}>
                Terminate
              </Button>
              <Button
                kind="secondary"
                isLoading={updateWorkSpaceMutation.isLoading}
                disabled={updateWorkSpaceMutation.isLoading}
                onClick={onUpdate}
                startEnhancer={<ArrowUp size={16} />}
              >
                Upgrade now
              </Button>
              <Button
                kind="secondary"
                onClick={() => onAction(detail?.status === 'Active' ? 'Pause' : 'Resume')}
                startEnhancer={detail?.status === 'Active' ? <PauseIcon size={16} /> : <PlayIcon size={16} />}
              >
                {detail?.status === 'Active' ? 'Pause' : 'Resume'}
              </Button>
            </div>
          )}
        </div>

        {error ? (
          <ErrorDisplay error={error as Error | AxiosError} />
        ) : isLoading ? (
          <LoadingIndicator />
        ) : detail ? (
          <div className="grid grid-cols-2 gap-8">
            <div className="space-y-1">
              {leftColumnFields.map((field, index) => renderField(field.label, field.value))}
            </div>
            <div className="space-y-1">
              {rightColumnFields.map((field, index) => renderField(field.label, field.value))}
              <TreeNode
                label={folderStatisticsTree.label}
                value={folderStatisticsTree.value}
                items={folderStatisticsTree.items}
              />
            </div>
          </div>
        ) : null}
      </div>
    </Modal>
  );
}
