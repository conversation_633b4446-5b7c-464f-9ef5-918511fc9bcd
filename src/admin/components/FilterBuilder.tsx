import { Button } from '@tigergraph/app-ui-lib/button';
import { Select } from '@tigergraph/app-ui-lib/select';
import { Input } from '@tigergraph/app-ui-lib/input';
import { FilterCondition, FilterOperator } from '@/admin/hooks/useAdminWorkspaces';
import { XIcon, CheckIcon, PlusIcon } from 'lucide-react';
import { useMemo, ChangeEvent, useState, useEffect } from 'react';

export type FilterField = {
  label: string;
  id: string;
};

const WORKSPACE_FILTER_FIELDS: FilterField[] = [
  { label: 'Name', id: 'workspaces.name' },
  { label: 'Creator', id: 'workspaces.creator' },
  { label: 'Workgroup ID', id: 'workspaces.workgroup_id' },
  { label: 'Workspace ID', id: 'workspaces.id' },
  { label: 'Database ID', id: 'workspaces.tg_database_id' },
  { label: 'Organization ID', id: 'workspaces.org_id' },
  { label: 'Organization Name', id: 'organization.org_name' },
  { label: 'Region', id: 'workspaces.region' },
  { label: 'Status', id: 'workspaces.status' },
  { label: 'Created At', id: 'workspaces.created_at' },
  { label: 'Type', id: 'workspaces.workspace_type_name' },
  { label: 'Version', id: 'workspaces.tg_version' },
  { label: 'Topology Size', id: 'heartbeat.graph_topology_size_bytes' },
  { label: 'Cloud Provider ID', id: 'workspaces.cloud_provider_id' },
  { label: 'Update Strategy', id: 'workspaces.update_strategy' },
  { label: 'Deleted At', id: 'workspaces.deleted_at' },
];

const ORG_FILTER_FIELDS: FilterField[] = [
  { label: 'Organization ID', id: 'org_id' },
  { label: 'Organization Name', id: 'org_name' },
  { label: 'Display Name', id: 'org_display_name' },
  { label: 'Creator', id: 'creator' },
  { label: 'Logo URL', id: 'logo_url' },
  { label: 'Created At', id: 'created_at' },
];

const BILLING_FILTER_FIELDS: FilterField[] = [
  { label: 'Customer Name', id: 'customer_name' },
  { label: 'Customer ID', id: 'customer_id' },
  { label: 'Email', id: 'emails' },
  { label: 'Tier', id: 'tier' },
];

const OPERATORS: Array<{ label: string; id: FilterOperator }> = [
  { label: 'equals', id: 'eq' },
  { label: 'not equals', id: 'neq' },
  { label: 'contains', id: 'like' },
  { label: 'not contains', id: 'notlike' },
  { label: 'greater than', id: 'gt' },
  { label: 'greater than or equals', id: 'gte' },
  { label: 'less than', id: 'lt' },
  { label: 'less than or equals', id: 'lte' },
];

const selectOverrides = {
  Root: {
    style: {
      height: '32px',
    },
  },
  ControlContainer: {
    style: {
      height: '32px',
    },
  },
  ValueContainer: {
    style: {
      fontSize: '14px',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      paddingTop: '0',
      paddingBottom: '0',
      paddingLeft: '0',
      paddingRight: '0',
    },
  },
  DropdownListItem: {
    style: {
      fontSize: '14px',
      textAlign: 'center' as const,
    },
  },
  SingleValue: {
    style: {
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      margin: '0',
      position: 'relative' as const,
      textAlign: 'center' as const,
      width: '100%',
    },
  },
  SelectArrow: {
    style: {
      width: '24px',
    },
  },
};

export interface FilterBuilderProps {
  filters: FilterCondition[];
  onChange: (filters: FilterCondition[], includeDeleted?: boolean) => void;
  hideIncludeDeleted?: boolean;
  type: 'workspace' | 'org' | 'billing';
}

export function FilterBuilder({ filters, onChange, hideIncludeDeleted, type }: FilterBuilderProps) {
  const [draftFilters, setDraftFilters] = useState<FilterCondition[]>(filters);
  const [includeDeleted, setIncludeDeleted] = useState(false);

  const filterFields =
    type === 'workspace' ? WORKSPACE_FILTER_FIELDS : type === 'billing' ? BILLING_FILTER_FIELDS : ORG_FILTER_FIELDS;

  // Sync draftFilters with external filters
  useEffect(() => {
    setDraftFilters(filters);
  }, [filters]);

  const handleAddFilter = () => {
    setDraftFilters([...draftFilters, { field: filterFields[0].id, operator: 'eq', value: '' }]);
  };

  const handleRemoveFilter = (index: number) => {
    const newFilters = [...draftFilters];
    newFilters.splice(index, 1);
    // Reassign indices after removal
    const updatedFilters = assignFilterIndices(newFilters);
    setDraftFilters(updatedFilters);
    // Immediately apply changes when removing a filter
    onChange(
      updatedFilters.filter((f) => f.value !== ''),
      includeDeleted
    );
  };

  const handleFilterChange = (index: number, field: string, value: any) => {
    const newFilters = [...draftFilters];
    newFilters[index] = { ...newFilters[index], [field]: value };
    // Reassign indices when field changes
    const updatedFilters = assignFilterIndices(newFilters);
    setDraftFilters(updatedFilters);
  };

  const assignFilterIndices = (filters: FilterCondition[]): FilterCondition[] => {
    return filters.map((filter, idx) => ({
      ...filter,
      index: idx,
    }));
  };

  const handleApplyFilters = () => {
    const filtersToApply = draftFilters.filter((f) => f.value !== '');
    onChange(filtersToApply, includeDeleted);
  };

  const activeFilters = useMemo(() => {
    return draftFilters.filter((f) => f.value !== '');
  }, [draftFilters]);

  return (
    <div className="space-y-2 bg-gray-50 p-4 rounded-lg">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button onClick={handleAddFilter} size="compact" kind="secondary" startEnhancer={<PlusIcon size={16} />}>
            Add filter
          </Button>
          {!hideIncludeDeleted && type === 'workspace' && (
            <div className="flex items-center gap-2">
              <input
                type="checkbox"
                id="include-deleted"
                className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                onChange={(e) => {
                  setIncludeDeleted(e.target.checked);
                  onChange(
                    draftFilters.filter((f) => f.value !== ''),
                    e.target.checked
                  );
                }}
                checked={includeDeleted}
              />
              <label htmlFor="include-deleted" className="text-sm text-gray-600">
                Include deleted
              </label>
            </div>
          )}
          {activeFilters.length > 0 && (
            <span className="text-sm text-gray-600">
              {activeFilters.length} active filter{activeFilters.length > 1 ? 's' : ''}
            </span>
          )}
        </div>
        {activeFilters.length > 0 && (
          <Button onClick={handleApplyFilters} size="compact" kind="primary" startEnhancer={<CheckIcon size={16} />}>
            Apply filters
          </Button>
        )}
      </div>

      <div className="space-y-2">
        {draftFilters.map((filter, index) => (
          <div key={index} className="grid grid-cols-[180px_140px_280px_40px] gap-2 bg-white p-2 rounded border">
            <div className="flex flex-col">
              <div className="text-sm mb-1 font-medium">Field</div>
              <Select
                size="compact"
                options={filterFields}
                value={[{ id: filter.field }]}
                onChange={({ value }) => handleFilterChange(index, 'field', value[0].id)}
                clearable={false}
                searchable={false}
                overrides={selectOverrides}
              />
            </div>
            <div className="flex flex-col">
              <div className="text-sm mb-1 font-medium">Operator</div>
              <Select
                size="compact"
                options={OPERATORS}
                value={[{ id: filter.operator || 'eq' }]}
                onChange={({ value }) => handleFilterChange(index, 'operator', value[0].id)}
                clearable={false}
                searchable={false}
                overrides={selectOverrides}
              />
            </div>
            <div className="flex flex-col">
              <div className="text-sm mb-1 font-medium">Value</div>
              <Input
                size="compact"
                value={filter.value as string}
                onChange={(e: ChangeEvent<HTMLInputElement>) => handleFilterChange(index, 'value', e.target.value)}
                placeholder="Enter value..."
                overrides={{
                  Root: {
                    style: {
                      height: '32px',
                    },
                  },
                  Input: {
                    style: {
                      fontSize: '14px',
                      textAlign: 'left',
                      paddingLeft: '8px',
                    },
                  },
                }}
              />
            </div>
            <div className="flex flex-col justify-end h-[47px]">
              <Button
                kind="secondary"
                size="compact"
                onClick={() => handleRemoveFilter(index)}
                overrides={{
                  Root: {
                    style: {
                      height: '32px',
                      width: '32px',
                      paddingLeft: '0',
                      paddingRight: '0',
                    },
                  },
                }}
              >
                <XIcon size={16} />
              </Button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
