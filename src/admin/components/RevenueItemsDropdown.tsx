import { useState, useRef, useEffect, useMemo } from 'react';
import { ChevronDownIcon, ChevronRightIcon } from 'lucide-react';
import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import { format } from 'date-fns';
import { Chart } from '@tigergraph/app-ui-lib/charts';
import { RevenueItem, GroupingInterval } from '@/admin/hooks/useAdminBilling';

interface RevenueItemsDropdownProps {
  customerName: string;
  revenueItems: RevenueItem[];
  totalRevenue: number;
  groupingInterval: GroupingInterval;
}

const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(amount);
};

export function RevenueItemsDropdown({
  customerName,
  revenueItems,
  totalRevenue,
  groupingInterval,
}: RevenueItemsDropdownProps) {
  const [css, theme] = useStyletron();
  const [isOpen, setIsOpen] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);

  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      switch (groupingInterval) {
        case 'HOUR':
          return format(date, 'MM/dd HH:mm');
        case 'DAY':
          return format(date, 'yyyy/MM/dd');
        case 'WEEK':
          return format(date, 'yyyy/MM/dd');
        case 'MONTH':
          return format(date, 'yyyy/MM');
        default:
          return format(date, 'yyyy/MM/dd');
      }
    } catch {
      return dateString;
    }
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen]);

  const chartOption = useMemo(() => {
    if (!revenueItems.length) {
      return null;
    }

    const formatDateInChart = (dateString: string) => {
      try {
        const date = new Date(dateString);
        switch (groupingInterval) {
          case 'HOUR':
            return format(date, 'MM/dd HH:mm');
          case 'DAY':
            return format(date, 'yyyy/MM/dd');
          case 'WEEK':
            return format(date, 'yyyy/MM/dd');
          case 'MONTH':
            return format(date, 'yyyy/MM');
          default:
            return format(date, 'yyyy/MM/dd');
        }
      } catch {
        return dateString;
      }
    };

    const chartData = revenueItems.map((item) => ({
      name: formatDateInChart(item.date),
      value: item.revenue,
    }));

    return {
      title: {
        text: 'Revenue Trend',
        subtext: customerName,
        left: 'center',
        top: 10,
        textStyle: {
          fontSize: 14,
          fontWeight: 'bold',
        },
        subtextStyle: {
          fontSize: 12,
          color: '#666',
        },
      },
      tooltip: {
        trigger: 'axis' as const,
        axisPointer: {
          type: 'shadow' as const,
        },
        formatter: (params: any) => {
          const item = params[0];
          return `${item.name}<br/>Revenue: ${formatCurrency(item.value)}`;
        },
      },
      grid: {
        left: '10%',
        right: '10%',
        bottom: revenueItems.length > 8 ? '20%' : '15%',
        top: '25%',
        containLabel: true,
      },
      ...(revenueItems.length > 8 && {
        dataZoom: [
          {
            type: 'slider',
            show: true,
            xAxisIndex: [0],
            start: 0,
            end: 100,
            height: 18,
            bottom: 20,
            borderColor: 'transparent',
            backgroundColor: '#f1f5f9',
            fillerColor: 'rgba(37, 99, 235, 0.1)',
            handleStyle: {
              color: '#2563eb',
              borderColor: '#2563eb',
            },
          },
        ],
      }),
      xAxis: {
        type: 'category' as const,
        data: chartData.map((item) => item.name),
        axisLabel: {
          rotate: revenueItems.length > 6 ? 45 : 0,
          fontSize: 10,
        },
        boundaryGap: true,
      },
      yAxis: {
        type: 'value' as const,
        splitLine: {
          lineStyle: {
            type: 'dashed' as const,
          },
        },
        axisLabel: {
          formatter: (value: number) => formatCurrency(value),
          fontSize: 10,
        },
      },
      series: [
        {
          name: 'Revenue',
          type: 'bar',
          data: chartData,
          barMaxWidth: 35,
          itemStyle: {
            color: '#10b981',
            borderRadius: [4, 4, 0, 0],
          },
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowColor: 'rgba(0,0,0,0.3)',
            },
          },
          label: {
            show: revenueItems.length <= 6,
            position: 'top',
            formatter: (params: any) => formatCurrency(params.value),
            fontSize: 9,
          },
        },
      ],
    };
  }, [revenueItems, customerName, groupingInterval]);

  if (!revenueItems.length) {
    return (
      <div className="w-6 h-6 flex items-center justify-center">
        <span className="text-gray-400">-</span>
      </div>
    );
  }

  const chartStyle = {
    width: '100%',
    height: '280px',
    backgroundColor: theme.colors['background.primary'],
    borderRadius: '6px',
  };

  return (
    <div ref={containerRef} className="relative">
      <button
        className="flex items-center justify-center w-6 h-6 text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded cursor-pointer transition-colors"
        onClick={() => setIsOpen(!isOpen)}
      >
        {isOpen ? <ChevronDownIcon size={14} /> : <ChevronRightIcon size={14} />}
      </button>

      {isOpen && (
        <div
          className={css({
            position: 'absolute',
            top: '100%',
            right: '200px',
            zIndex: 1000,
            backgroundColor: theme.colors['background.primary'],
            border: `1px solid ${theme.colors['border.tertiary']}`,
            borderRadius: '8px',
            padding: '16px',
            minWidth: '800px',
            maxWidth: '1000px',
            boxShadow: '0 8px 24px rgba(0, 0, 0, 0.15)',
            marginTop: '4px',
          })}
        >
          <div className="mb-4">
            <div className="font-semibold text-sm text-gray-900">Revenue Chart</div>
            <div className="text-xs text-gray-600 truncate">{customerName}</div>
          </div>

          <div className="mb-4">
            <Chart option={chartOption} style={chartStyle} />
          </div>

          <div className="pt-3 border-t border-gray-200">
            <div className="flex justify-between items-center text-sm font-semibold">
              <span className="text-gray-700">Total Revenue:</span>
              <span className="font-mono text-green-600">{formatCurrency(totalRevenue)}</span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
