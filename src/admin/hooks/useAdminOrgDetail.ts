import { AdminOrg } from '@/admin/hooks/useAdminOrgs';
import { axiosController } from '@/lib/network';
import { Result } from '@/lib/type';
import { useQuery } from 'react-query';

export type AdminOrganizationDetailType = Omit<AdminOrg, 'feature_flag'> & {
  org_name: string;
  workspace_count: number;
  workgroup_count: number;
  tg_database_count: number;
  user_count: number;
  credits: number;
  payment_methods: {
    default_card: string;
    credit_cards: {
      card_id: string;
      last_4_digits: string;
      name_on_card: string;
      brand: string;
    }[];
  };
  quota: {
    tier: string;
    max_workspace_type: string;
    max_workspace_memory: string;
    rw_workspace_count_limit: number;
    ro_workspace_count_limit: number;
    memory_limit: string;
    workspace_auto_backup_count_limit: number;
    workspace_auto_backup_retention_in_days: number;
    workspace_manual_backup_count_limit: number;
    rw_workspace_count_usage: number;
    ro_workspace_count_usage: number;
    memory_usage_in_bytes: number;
  };
  feature_flag: {
    feature_name: string;
    enabled: boolean;
  }[];
  tg_databases: {
    id: string;
    name: string;
    status: string;
    created_at: string;
  }[];
  workgroups: any[];
  users: any[];
};

export const useAdminOrgDetail = ({ orgID, enabled = true }: { orgID?: string; enabled?: boolean }) => {
  return useQuery({
    queryKey: ['admin', 'org', 'detail', orgID],
    queryFn: async () => {
      const res = await axiosController.get<Result<AdminOrganizationDetailType>>(`/admin/orgs/${orgID}`);
      return res.data.Result;
    },
    enabled: Boolean(orgID && enabled),
  });
};
