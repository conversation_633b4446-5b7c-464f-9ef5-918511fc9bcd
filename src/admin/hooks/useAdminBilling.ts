import { axiosController } from '@/lib/network';
import { Result } from '@/lib/type';
import { useQuery } from 'react-query';

export type FilterOperator = 'gt' | 'gte' | 'lt' | 'lte' | 'eq' | 'neq' | 'like' | 'notlike';

export type FilterCondition = {
  field: string;
  operator?: FilterOperator;
  value: string | number | boolean;
  index?: number;
};

export type RevenueItem = {
  date: string;
  revenue: number;
};

export type CustomerRevenue = {
  customer_id: string;
  total_revenue: number;
  revenue_items: RevenueItem[];
};

export type CustomerBillingData = {
  customer: string;
  customer_id: string;
  email: string;
  tier: string;
  default_payment: string;
  tigergraph: boolean;
  customer_revenue: CustomerRevenue;
};

export type AdminBillingSort =
  | 'customer_infos.created_at'
  | 'customer_infos.customer_name'
  | 'customer_infos.emails'
  | 'quota.tier'
  | 'total_revenue';

export type GroupingInterval = 'HOUR' | 'DAY' | 'WEEK' | 'MONTH';

export interface AdminBillingListParams {
  from?: string; // RFC3339 format
  to?: string; // RFC3339 format
  filters?: FilterCondition[];
  page?: number;
  pageSize?: number;
  sort?: AdminBillingSort;
  sortDirection?: 'asc' | 'desc';
  groupingInterval?: GroupingInterval;
}

export type AdminBillingPaginatedData = {
  customer_billing_data: CustomerBillingData[];
  total: number;
};

export const buildFilterParam = (condition: FilterCondition): [string, string | number | boolean] => {
  const { field, operator, value, index } = condition;
  const indexSuffix = typeof index === 'number' ? `:${index}` : '';
  return [`filter[${field}:${operator}${indexSuffix}]`, value];
};

export function useAdminBilling(params?: AdminBillingListParams) {
  const {
    from,
    to,
    filters,
    page = 1,
    pageSize = 100,
    sort = 'customer_infos.created_at',
    sortDirection = 'desc',
    groupingInterval = 'MONTH',
  } = params || {};

  return useQuery<AdminBillingPaginatedData, Error>({
    queryKey: ['admin', 'billing', { from, to, filters, page, pageSize, sort, sortDirection, groupingInterval }],
    queryFn: async () => {
      const queryParams: Record<string, any> = {
        page,
        page_size: pageSize,
        grouping_interval: groupingInterval,
      };

      if (from) queryParams.from = from;
      if (to) queryParams.to = to;
      if (sort) queryParams.sort = `${sort}:${sortDirection}`;

      if (filters?.length) {
        filters.forEach((filter) => {
          const [key, value] = buildFilterParam(filter);
          queryParams[key] = value;
        });
      }

      const res = await axiosController.get<Result<AdminBillingPaginatedData>>('/admin/billing/data', {
        params: queryParams,
      });

      if (!res.data.Result) {
        throw new Error('No data returned from server');
      }

      return res.data.Result;
    },
  });
}

export function useAdminBillingAll(params?: AdminBillingListParams) {
  const {
    from,
    to,
    filters,
    sort = 'customer_infos.created_at',
    sortDirection = 'desc',
    groupingInterval = 'MONTH',
  } = params || {};

  return useQuery<AdminBillingPaginatedData, Error>({
    queryKey: ['admin', 'billing', 'all', { from, to, filters, sort, sortDirection, groupingInterval }],
    queryFn: async () => {
      const queryParams: Record<string, any> = {
        grouping_interval: groupingInterval,
      };

      if (from) queryParams.from = from;
      if (to) queryParams.to = to;
      if (sort) queryParams.sort = `${sort}:${sortDirection}`;

      if (filters?.length) {
        filters.forEach((filter) => {
          const [key, value] = buildFilterParam(filter);
          queryParams[key] = value;
        });
      }

      const res = await axiosController.get<Result<AdminBillingPaginatedData>>('/admin/billing/data/all', {
        params: queryParams,
      });

      if (!res.data.Result) {
        throw new Error('No data returned from server');
      }

      return res.data.Result;
    },
  });
}
