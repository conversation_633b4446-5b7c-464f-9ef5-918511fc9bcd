import { useMutation } from 'react-query';
import { axiosController } from '@/lib/network';
import { Result } from '@/lib/type';

export type FeatureFlag = {
  feature_name: string;
  enabled: boolean;
};

export type FeatureFlagRequest = {
  feature_flags: FeatureFlag[];
};

export function useAddOrgFeatureFlag(orgID: string, options?: any) {
  return useMutation<
    unknown, // response data type (let TS infer)
    unknown, // error type (let TS infer)
    FeatureFlag[] // variables type
  >({
    mutationFn: async (featureFlags: FeatureFlag[]) => {
      const res = await axiosController.post<Result<null>>(`/admin/orgs/${orgID}/feature_flag`, {
        feature_flags: featureFlags,
      });
      return res.data;
    },
    ...options,
  });
}

export function useDeleteOrgFeatureFlag(orgID: string, options?: any) {
  return useMutation<
    unknown, // response data type (let TS infer)
    unknown, // error type (let TS infer)
    FeatureFlag[] // variables type
  >({
    mutationFn: async (featureFlags: FeatureFlag[]) => {
      const res = await axiosController.delete<Result<null>>(`/admin/orgs/${orgID}/feature_flag`, {
        data: { feature_flags: featureFlags },
      });
      return res.data;
    },
    ...options,
  });
}
