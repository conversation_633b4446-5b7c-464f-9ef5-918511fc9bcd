import { createContext, useContext, useState, ReactNode } from 'react';
import { GLOBAL_GRAPH_NAME } from '@tigergraph/tools-models';

interface GraphExpandedState {
  // GraphStore expanded states: graphName -> boolean
  graphStores: Record<string, boolean>;
  // SchemaList expanded states: graphName -> boolean
  schemaLists: Record<string, boolean>;
  // QueryList expanded states: graphName -> boolean
  queryLists: Record<string, boolean>;
  // Track if initial auto-expansion has been performed
  hasInitializedAutoExpansion: boolean;
}

interface GraphExpandedContextType {
  expandedState: GraphExpandedState;
  setGraphStoreExpanded: (graphName: string, expanded: boolean) => void;
  setSchemaListExpanded: (graphName: string, expanded: boolean) => void;
  setQueryListExpanded: (graphName: string, expanded: boolean) => void;
  getGraphStoreExpanded: (graphName: string) => boolean;
  getSchemaListExpanded: (graphName: string) => boolean;
  getQueryListExpanded: (graphName: string) => boolean;
  ensureFirstGraphExpanded: (graphNames: string[]) => void;
}

const GraphExpandedContext = createContext<GraphExpandedContextType | undefined>(undefined);

export function GraphExpandedProvider({ children }: { children: ReactNode }) {
  const [expandedState, setExpandedState] = useState<GraphExpandedState>({
    graphStores: {},
    schemaLists: {},
    queryLists: {},
    hasInitializedAutoExpansion: false,
  });

  const setGraphStoreExpanded = (graphName: string, expanded: boolean) => {
    setExpandedState(prev => ({
      ...prev,
      graphStores: {
        ...prev.graphStores,
        [graphName]: expanded,
      },
    }));
  };

  const setSchemaListExpanded = (graphName: string, expanded: boolean) => {
    setExpandedState(prev => ({
      ...prev,
      schemaLists: {
        ...prev.schemaLists,
        [graphName]: expanded,
      },
    }));
  };

  const setQueryListExpanded = (graphName: string, expanded: boolean) => {
    setExpandedState(prev => ({
      ...prev,
      queryLists: {
        ...prev.queryLists,
        [graphName]: expanded,
      },
    }));
  };

  const getGraphStoreExpanded = (graphName: string) => {
    return expandedState.graphStores[graphName] ?? false;
  };

  const getSchemaListExpanded = (graphName: string) => {
    return expandedState.schemaLists[graphName] ?? false;
  };

  const getQueryListExpanded = (graphName: string) => {
    return expandedState.queryLists[graphName] ?? false;
  };

  const ensureFirstGraphExpanded = (graphNames: string[]) => {
    if (!graphNames.length) {
      return;
    }

    // Only run auto-expansion once, and only if it hasn't been initialized yet
    if (expandedState.hasInitializedAutoExpansion) {
      return;
    }

    // Filter out GLOBAL_GRAPH_NAME and get the first valid graph
    const validGraphs = graphNames.filter(name => name !== GLOBAL_GRAPH_NAME);

    if (validGraphs.length > 0) {
      const firstGraph = validGraphs[0];

      // Check if any graph is already expanded
      const hasExpandedGraph = validGraphs.some(graphName =>
        expandedState.graphStores[graphName] === true
      );

      // Mark as initialized and expand first graph if none are expanded
      setExpandedState(prev => ({
        ...prev,
        hasInitializedAutoExpansion: true,
        graphStores: hasExpandedGraph ? prev.graphStores : {
          ...prev.graphStores,
          [firstGraph]: true,
        },
      }));
    } else {
      // Mark as initialized even if no valid graphs
      setExpandedState(prev => ({
        ...prev,
        hasInitializedAutoExpansion: true,
      }));
    }
  };

  return (
    <GraphExpandedContext.Provider
      value={{
        expandedState,
        setGraphStoreExpanded,
        setSchemaListExpanded,
        setQueryListExpanded,
        getGraphStoreExpanded,
        getSchemaListExpanded,
        getQueryListExpanded,
        ensureFirstGraphExpanded,
      }}
    >
      {children}
    </GraphExpandedContext.Provider>
  );
}

export function useGraphExpanded() {
  const context = useContext(GraphExpandedContext);
  if (context === undefined) {
    throw new Error('useGraphExpanded must be used within a GraphExpandedProvider');
  }
  return context;
}
