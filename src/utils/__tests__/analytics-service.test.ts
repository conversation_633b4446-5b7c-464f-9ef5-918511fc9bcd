import { describe, it, expect, beforeEach, vi } from 'vitest';
import mixpanel from 'mixpanel-browser';
import trackUtil from '../analytics-service';

// Mock mixpanel
vi.mock('mixpanel-browser', () => ({
  default: {
    init: vi.fn(),
    register: vi.fn(),
    opt_in_tracking: vi.fn(),
    track: vi.fn(),
    identify: vi.fn(),
  },
}));

describe('AnalyticsService', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('TigerGraph email filtering', () => {
    it('should skip tracking for users with tigergraph.com email', () => {
      // Login with tigergraph.com email
      trackUtil.trackLoginComplete({
        tenantID: 'test-tenant',
        id: 'test-id',
        email: '<EMAIL>',
      });

      // Verify identify was called (this happens before filtering)
      expect(mixpanel.identify).not.toHaveBeenCalledWith('test-tenant_test-id');

      // Clear the mock to test subsequent tracking calls
      vi.clearAllMocks();

      // Try to track another event
      trackUtil.trackCreateWorkspace({ status: 'Success' as any });

      // Verify that track was NOT called due to filtering
      expect(mixpanel.track).not.toHaveBeenCalled();
    });

    it('should allow tracking for users with non-tigergraph.com email', () => {
      // Login with external email
      trackUtil.trackLoginComplete({
        tenantID: 'test-tenant',
        id: 'test-id',
        email: '<EMAIL>',
      });

      // Verify identify was called
      expect(mixpanel.identify).toHaveBeenCalledWith('test-tenant_test-id');

      // Clear the mock to test subsequent tracking calls
      vi.clearAllMocks();

      // Try to track another event
      trackUtil.trackCreateWorkspace({ status: 'Success' as any });

      // Verify that track WAS called
      expect(mixpanel.track).toHaveBeenCalledWith('Create Workspace', { status: 'Success' });
    });
  });
});
