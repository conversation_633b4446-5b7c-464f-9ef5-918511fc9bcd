export function initChatbot(email: string) {
  (function (d: Document, w: any, c: string) {
    w.AppUrl = `chatbot/c25b78f8-458c-4111-9fc7-d5823c502aa0/8f629408-f302-44f4-81fc-cf85851507d9?mode=embed&user=${encodeURIComponent(
      email
    )}`;
    w.themeColor = '#ef873a';
    var s = d.createElement('script');
    w[c] =
      w[c] ||
      function () {
        (w[c].q = w[c].q || []).push(arguments);
      };
    s.async = true;
    s.src = 'https://script.epsilla.com/epsilla.js';
    if (d.head) d.head.appendChild(s);
  })(document, window, 'Epsilla');
}

// This script is used to hide the chat button when the drawer is open
// Function to check if the element with data-baseweb="drawer" exists
function hideChatbot() {
  if (window.location.pathname.startsWith('/dashboard')) {
    return true;
  }
  return (
    document.querySelector('[data-baseweb="drawer"]') !== null ||
    document.querySelector('[data-baseweb="hide-chat"]') !== null
  );
}

// Function to hide or display the button based on the drawer's existence
function updateButtonVisibility() {
  const button = document.querySelector('body > button') as HTMLElement;
  const chatWindow = document.querySelector('#frameContainer') as HTMLElement;
  if (!button || !chatWindow) return;
  button.style.right = '38px';
  button.style.bottom = '38px';
  if (button) button.style.display = hideChatbot() ? 'none' : 'flex';
  if (chatWindow) chatWindow.style.display = hideChatbot() ? 'none' : 'block';
}

// MutationObserver callback function
const observerCallback = (mutationsList: MutationRecord[]) => {
  for (const mutation of mutationsList) {
    if (mutation.type === 'childList' || mutation.type === 'attributes') {
      updateButtonVisibility();
    }
  }
};

// Create a MutationObserver instance
const observer = new MutationObserver(observerCallback);

// Start observing the target node for configured mutations
const config = { childList: true, subtree: true, attributes: true, attributeFilter: ['data-baseweb'] };
observer.observe(document, config);

// Initial check
updateButtonVisibility();
