import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import { Button, SIZE } from '@tigergraph/app-ui-lib/button';

interface ConfirmButtonsProps {
  confirmLabel?: string;
  cancelLabel?: string;
  onConfirm?: () => void;
  onCancel?: () => void;
  confirmDisabled?: boolean;
  cancelDisabled?: boolean;
  confirmLoading?: boolean;
  size?: SIZE;
}

export default function ConfirmButtons(props: ConfirmButtonsProps) {
  const [css] = useStyletron();

  const {
    confirmLabel = 'Confirm',
    cancelLabel = 'Cancel',
    onConfirm,
    onCancel,
    confirmDisabled,
    cancelDisabled,
    confirmLoading,
    size = 'large',
  } = props;
  return (
    <div
      className={css({
        display: 'flex',
        justifyContent: 'flex-end',
      })}
    >
      {onCancel && (
        <Button kind="secondary" type="button" disabled={cancelDisabled} size={size} onClick={onCancel}>
          {cancelLabel}
        </Button>
      )}
      {onConfirm && (
        <Button
          kind="primary"
          type="button"
          size={size}
          disabled={confirmDisabled}
          isLoading={confirmLoading}
          onClick={onConfirm}
          overrides={{
            BaseButton: {
              style: {
                marginLeft: '16px',
              },
            },
          }}
        >
          {confirmLabel}
        </Button>
      )}
    </div>
  );
}
