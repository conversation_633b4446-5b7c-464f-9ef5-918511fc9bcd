import { useMemo } from 'react';
import { PhotoLibrary } from '@/pages/home/<USER>';
import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import { StatefulPopover } from '@tigergraph/app-ui-lib/popover';
import { TRIGGER_TYPE, PLACEMENT } from 'baseui/popover';
import { expand } from 'inline-style-expand-shorthand';
import { getUserUploadedIconPathPrefix } from '@tigergraph/tools-ui/esm/insights/utils/path';
import { iconMap } from '@tigergraph/tools-ui/esm/graph/icons/iconMap';
import { Button } from '@tigergraph/app-ui-lib/button';
import { useTheme } from '@/contexts/themeContext';
import { getBaseURL } from '@tigergraph/tools-models';
import { SelectLucideIconContainer } from '@tigergraph/tools-ui/esm/insights/components/selectLucideIcon';

export interface IconSelectorProps {
  onSelect: (iconName: string) => void;
  onDelete?: (iconName: string) => void;
  value?: string;
  readOnly?: boolean;
  mountNode?: HTMLElement;
}

export function IconSelector({ onSelect, onDelete, value, mountNode, readOnly }: IconSelectorProps) {
  const [css, theme] = useStyletron();
  const { themeType } = useTheme();

  const iconUrl = useMemo(() => {
    if (!value) {
      return '';
    }
    const userUploadedIconPathPrefix = getUserUploadedIconPathPrefix(true);
    const iconUrl =
      value && value.includes('/studio/assets/img/')
        ? value
        : value && iconMap[value]
        ? `${getBaseURL() || ''}/studio/assets/gvis/icons/builtin/${iconMap[value]}`
        : userUploadedIconPathPrefix && value
        ? `${userUploadedIconPathPrefix}/${value}`
        : '';
    return iconUrl;
  }, [value]);

  return (
    <StatefulPopover
      showArrow={false}
      mountNode={mountNode}
      triggerType={TRIGGER_TYPE.click}
      placement={PLACEMENT.leftTop}
      overrides={{
        Body: {
          style: {
            padding: '8px',
            width: '300px',
            backgroundColor: theme.colors['background.primary'],
            ...expand({
              borderRadius: '2px',
              border: `1px solid ${theme.colors.divider}`,
            }),
          },
        },
        Inner: {
          style: {
            ...expand({
              padding: '8px',
            }),
            backgroundColor: theme.colors['background.primary'],
            color: theme.colors.contentPrimary,
          },
        },
      }}
      content={({ close }) => {
        return (
          <div onKeyDown={(e) => e.stopPropagation()}>
            <SelectLucideIconContainer
              iconURL={iconUrl}
              onIconSelected={(iconURL) => {
                if (iconURL.includes('/user-uploaded-icons/')) {
                  iconURL = iconURL?.split('/').pop() || '';
                }
                onSelect(iconURL);
                close();
              }}
              isCloud={true}
              height={250}
            />
          </div>
        );
      }}
    >
      <div>
        <StatefulPopover
          triggerType={TRIGGER_TYPE.hover}
          content={'Select icon'}
          overrides={{
            Body: {
              style: {
                ...expand({
                  margin: '10px',
                }),
              },
            },
          }}
        >
          <Button
            kind="secondary"
            disabled={readOnly}
            overrides={{
              BaseButton: {
                style: {
                  width: '32px',
                  height: '32px',
                  ...expand({
                    padding: '2px',
                  }),
                },
              },
            }}
          >
            {iconUrl ? (
              <img
                src={iconUrl}
                className={css({
                  filter:
                    themeType === 'light' && value && (!!iconMap[value] || value.includes('/studio/assets/img/'))
                      ? 'none'
                      : 'invert(1)',
                })}
                alt=""
              />
            ) : (
              <PhotoLibrary />
            )}
          </Button>
        </StatefulPopover>
      </div>
    </StatefulPopover>
  );
}
