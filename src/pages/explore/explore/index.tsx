import { useTheme } from '@/contexts/themeContext';
import { WorkspaceT } from '@/pages/workgroup/type';
import { Explore } from '@tigergraph/tools-ui/esm/explore';
import SendIcon from './send.svg?react';
import { ExternalGraph } from '@tigergraph/tools-models';

export default function GraphExplore({
  graphs,
  graph,
  currentWorkspace,
  initialGraphData,
  defaultVertexEdgeType,
}: {
  graphs: string[];
  graph: string;
  currentWorkspace: WorkspaceT;
  initialGraphData?: ExternalGraph;
  defaultVertexEdgeType?: string;
}) {
  const { themeType } = useTheme();
  return (
    <Explore
      key={graph}
      graphName={graph}
      graphs={graphs}
      isClusterMode={currentWorkspace.enable_ha || currentWorkspace.workspace_type.partition > 1}
      baseURL={`https://${currentWorkspace.nginx_host}`}
      isCloud={true}
      themeType={themeType}
      placeHolder="Enter a pattern to start exploring your graph or select the queries"
      sendIcon={<SendIcon height={16} width={16} />}
      isEditMode={currentWorkspace.is_rw}
      initialGraphData={initialGraphData}
      defaultVertexEdgeType={defaultVertexEdgeType}
    />
  );
}
