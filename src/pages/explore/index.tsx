import { Tab, Tabs } from '@/components/Tab';
import { WorkspaceSelector } from '@/pages/editor/WorkspaceSelector';
import { useWorkspaceContext } from '@/contexts/workspaceContext';
import GraphExplorer from '@/pages/explore/explore';
import { CustomTheme, useStyletron } from '@tigergraph/app-ui-lib/Theme';
import { TabOverrides } from 'baseui/tabs-motion';
import { useLocation, useSearchParams } from 'react-router-dom';
import { GraphSelector } from './explore/GraphSelector';
import { Layer } from 'baseui/layer';
import EmptyState from '@/pages/workgroup/EmptyState';
import NoAccess from '@/pages/workgroup/NoAccess';
import NoGraph from '@/pages/dashboard/NoGraph';
import { ExternalGraph, GLOBAL_GRAPH_NAME } from '@tigergraph/tools-models';

export default function ExplorePage() {
  const [searchParams, setSearchParams] = useSearchParams();

  const location = useLocation();
  const locationState = location.state as {
    graphData?: ExternalGraph;
    defaultVertexEdgeType?: string;
  } | null;

  const [css, theme] = useStyletron();
  const { currentWorkspace, workspaces, setCurrentGraph, currentGraph, graphNames, isFetchingSimpleAuth } =
    useWorkspaceContext();

  const graphNamesExcludeGlobal = graphNames.filter((g) => g !== GLOBAL_GRAPH_NAME);
  const currentGraphExcludeGlobal = currentGraph !== GLOBAL_GRAPH_NAME ? currentGraph : '';

  if (workspaces.length === 0) {
    return (
      <div
        className={css({
          height: '100vh',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
        })}
      >
        <EmptyState />
      </div>
    );
  }

  return (
    <div
      className={css({
        paddingTop: '16px',
        height: '100vh',
        backgroundColor: theme.colors['background.secondary'],
      })}
    >
      <Layer>
        <div
          className={css({
            position: 'absolute',
            top: 0,
            right: 0,
            display: 'flex',
            justifyContent: 'flex-end',
          })}
          key="header"
        >
          <div
            className={css({
              display: 'flex',
              justifyContent: 'flex-end',
              padding: '12px 0',
            })}
          >
            <WorkspaceSelector />
            <div
              className={css({
                width: '200px',
                marginRight: '16px',
              })}
            >
              {/*
                we should use GraphSelect component, but
                1. Graph Widget do not support global graph
                2. Do not need delete and create graph
                so we just sync graph state between graph explorer and workspace context
              */}
              <GraphSelector
                graphs={graphNamesExcludeGlobal}
                graph={currentGraphExcludeGlobal}
                onGraphChanged={(graphName) => {
                  setCurrentGraph(graphName);
                }}
                isFetching={isFetchingSimpleAuth}
              />
            </div>
          </div>
        </div>
      </Layer>
      <Tabs
        activeKey={searchParams.get('tab') || 'explore'}
        onChange={({ activeKey }) => {
          let newSearchParams = new URLSearchParams(searchParams);
          newSearchParams.set('tab', activeKey as string);
          setSearchParams(newSearchParams, { replace: true });
        }}
        activateOnFocus
        overrides={{
          TabHighlight: {
            style: {
              display: 'none',
            },
          },
        }}
        renderAll={true}
      >
        <Tab title="Explore Graph" key="explore" overrides={generateTabOverrides(theme)}>
          {currentWorkspace ? (
            !currentWorkspace.canAccess ? (
              <NoAccess workspace={currentWorkspace} />
            ) : !isFetchingSimpleAuth && graphNamesExcludeGlobal.length === 0 ? (
              <div
                className={css({
                  height: '100%',
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'center',
                  alignItems: 'center',
                })}
              >
                <NoGraph space_id={currentWorkspace.workspace_id} />
              </div>
            ) : (
              !isFetchingSimpleAuth && (
                <GraphExplorer
                  graphs={graphNamesExcludeGlobal}
                  graph={currentGraphExcludeGlobal}
                  currentWorkspace={currentWorkspace}
                  initialGraphData={locationState?.graphData}
                  defaultVertexEdgeType={locationState?.defaultVertexEdgeType}
                />
              )
            )
          ) : (
            <EmptyState />
          )}
        </Tab>
      </Tabs>
    </div>
  );
}

function generateTabOverrides(theme: CustomTheme) {
  const tabOverrides: TabOverrides = {
    TabPanel: {
      style: {
        paddingLeft: '0',
        paddingRight: '0',
        paddingBottom: '0',
        paddingTop: '0',
        height: 'calc(100vh - 57px)', // Subtract the top and bottom padding

        boxSizing: 'border-box', // Include padding and border in element's total height and width
        position: 'relative',
        backgroundColor: theme.colors['background.tertiary.a'],
      },
    },

    Tab: {
      style: {
        ':hover': {
          background: 'transparent',
        },
        cursor: 'default',
      },
    },
  };
  return tabOverrides;
}
