import SimpleTypeParamInput from '@/pages/editor/query/params/SimpleTypeParamInput';
import { getSimpleTypeDefaultValue } from '@/utils/queryParam';
import { Button } from '@tigergraph/app-ui-lib/button';
import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import { QueryParam, QueryParamMapType } from '@tigergraph/tools-models';
import { BiPlus } from 'react-icons/bi';
import { MdDelete } from 'react-icons/md';

interface MapParamInputProps {
  param: QueryParam;
  value: { key: any; value: any }[];
  onChange: (value: { key: any; value: any }[]) => void;
  disabled?: boolean;
  error?: { key: string; value: string }[];
}

export default function MapParamInput({ param, value, onChange, disabled = false, error = [] }: MapParamInputProps) {
  const [css, theme] = useStyletron();
  const mapType = param.paramType as QueryParamMapType;
  const keyType = mapType.keyType;
  const valueType = mapType.valueType;

  const handleAddItem = () => {
    onChange([...value, { key: getSimpleTypeDefaultValue(keyType), value: getSimpleTypeDefaultValue(valueType) }]);
  };

  const handleRemoveItem = (idx: number) => {
    const newValue = [...value];
    newValue.splice(idx, 1);
    onChange(newValue);
  };

  const handleKeyChange = (idx: number, newKey: string) => {
    const newValue = [...value];
    newValue[idx].key = newKey;
    onChange(newValue);
  };

  const handleValueChange = (idx: number, newItemValue: any) => {
    const newValue = [...value];
    newValue[idx].value = newItemValue;
    onChange(newValue);
  };

  return (
    <div
      className={css({
        display: 'flex',
        flexDirection: 'column',
        gap: '8px',
        padding: '12px',
        backgroundColor: theme.colors['list.background.hover'],
        borderRadius: '4px',
      })}
    >
      {value.length > 0 && (
        <div className={css({
          display: 'grid',
          gridTemplateColumns: '1fr 1fr auto',
          gap: '8px',
          fontWeight: 500,
          fontSize: '14px',
          color: theme.colors['dropdown.text'],
        })}>
          <div>Key</div>
          <div>Value</div>
          <div className='w-[32px]'></div>
        </div>
      )}

      {value.map(({ key, value }, index) => (
        <div
          key={index}
          className={css({
            display: 'grid',
            gridTemplateColumns: '1fr 1fr auto',
            gap: '8px',
            alignItems: 'center',
          })}
        >
          <SimpleTypeParamInput
            param={{ paramName: 'key', paramType: keyType }}
            value={key}
            onChange={(newKey) => handleKeyChange(index, newKey)}
            disabled={disabled}
            error={error[index]?.key}
            isListItem={true}
          />
          <SimpleTypeParamInput
            param={{ paramName: 'value', paramType: valueType }}
            value={value}
            onChange={(newValue) => handleValueChange(index, newValue)}
            disabled={disabled}
            error={error[index]?.value}
            isListItem={true}
          />
          <Button kind="text" size="compact" onClick={() => handleRemoveItem(index)} disabled={disabled}>
            <MdDelete size={16} color={theme.colors['text.danger']} />
          </Button>
        </div>
      ))}

      <div className={css({ alignSelf: 'flex-start' })}>
        <Button
          kind="text"
          size="compact"
          onClick={handleAddItem}
          disabled={disabled}
          startEnhancer={<BiPlus size={16} color={theme.colors['text.link']} />}
        >
          <span className={css({ color: theme.colors['text.link'] })}>Add Item</span>
        </Button>
      </div>
    </div>
  );
}
