import { Input } from '@tigergraph/app-ui-lib/input';
import { Select } from '@tigergraph/app-ui-lib/select';
import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import { QueryParam } from '@tigergraph/tools-models';
import { ChangeEvent } from 'react';

interface SimpleTypeParam {
  param: QueryParam;
  value: any;
  onChange: (value: any) => void;
  error?: string;
  disabled?: boolean;
  isListItem?: boolean;
}

export default function SimpleTypeParamInput({ param, value, onChange, disabled = false, error, isListItem = false }: SimpleTypeParam) {
  const [css, theme] = useStyletron();

  const { paramType } = param;

  const renderInput = () => {
    switch (paramType.type) {
      case 'INT':
      case 'UINT':
        return (
          <Input
            value={value}
            onChange={(e: ChangeEvent<HTMLInputElement>) => onChange(e.target.value)}
            type="number"
            disabled={disabled}
            placeholder={paramType.type}
          />
        );
      case 'FLOAT':
      case 'DOUBLE':
        return (
          <Input
            value={value}
            onChange={(e: ChangeEvent<HTMLInputElement>) => onChange(e.target.value)}
            disabled={disabled}
            type="number"
            placeholder={paramType.type}
          />
        );
      case 'STRING':
        return (
          <Input
            value={value}
            onChange={(e: ChangeEvent<HTMLInputElement>) => onChange(e.target.value)}
            disabled={disabled}
            placeholder={paramType.type}
          />
        );
      case 'BOOL':
        return (
          <Select
            options={[
              { id: 'true', label: 'TRUE' },
              { id: 'false', label: 'FALSE' },
            ]}
            value={[{ id: value ? 'true' : 'false' }]}
            onChange={({ value }) => onChange(value[0].id === 'true')}
            clearable={false}
            disabled={disabled}
          />
        );
      case 'DATETIME':
        return (
          <Input
            value={value}
            onChange={(e: ChangeEvent<HTMLInputElement>) => onChange(e.target.value)}
            disabled={disabled}
            placeholder="YYYY-MM-DD HH:MM:SS"
          />
        );
      default:
        return (
          <Input
            value={value}
            onChange={(e: ChangeEvent<HTMLInputElement>) => onChange(e.target.value)}
            disabled={disabled}
            placeholder={paramType.type}
          />
        );
    }
  };

  return (
    <>
      {renderInput()}
      {error && !isListItem && (
        <div className={css({ color: theme.colors['text.danger'], fontSize: '12px', marginTop: '4px' })}>{error}</div>
      )}
    </>
  );
}
