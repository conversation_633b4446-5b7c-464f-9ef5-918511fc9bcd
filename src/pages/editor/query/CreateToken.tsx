import { ID_TOKEN_KEY } from '@/contexts/workspaceContext';
import { Return } from '@/lib/type';
import { WorkspaceT } from '@/pages/workgroup/type';
import { createSecret, UserSecret } from '@tigergraph/tools-models';
import { <PERSON><PERSON>, Modal<PERSON>ody, Modal<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eader } from '@tigergraph/app-ui-lib/modal';
import { Input } from '@tigergraph/app-ui-lib/input';
import { useState } from 'react';
import { showToast, StyledToast } from '@tigergraph/app-ui-lib/styledToasterContainer';
import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import { CopyIcon } from 'lucide-react';
import { Button } from '@tigergraph/app-ui-lib/button';
import { getErrorMessage } from '@/utils/utils';
import { KIND } from 'baseui/toast';
import { axiosCluster } from '@/lib/network';

async function createTokenReq(alias: string, wp: WorkspaceT) {
  const secretRes = (
    await createSecret(
      { alias },
      {
        baseURL: `https://${wp.nginx_host}`,
        version: wp.tg_version,
        headers: {
          Authorization: `Bearer ${sessionStorage.getItem(ID_TOKEN_KEY)}`,
        },
      }
    )
  ).data as Return<UserSecret>;
  if (secretRes.error) {
    throw new Error(secretRes.message);
  }

  // TODO version < 4.1 does not have this API
  const tokenRes = (
    await axiosCluster.post(
      '/api/gsql-server/gsql/v1/tokens',
      { secret: secretRes.results?.value },
      { baseURL: `https://${wp.nginx_host}` }
    )
  ).data as Return<{ token: string }>;
  if (tokenRes.error) {
    throw new Error(tokenRes.message);
  }
  return tokenRes.results?.token || '';
}

interface CreateTokenProps {
  isOpen: boolean;
  onClose: () => void;
  workspace: WorkspaceT;
}

type CreateTokenState = 'input' | 'result';

export function CreateToken({ isOpen, onClose, workspace }: CreateTokenProps) {
  const [css, theme] = useStyletron();
  const [state, setState] = useState<CreateTokenState>('input');
  const [alias, setAlias] = useState('');
  const [token, setToken] = useState('');
  const [loading, setLoading] = useState(false);

  const handleClose = () => {
    setState('input');
    setAlias('');
    setToken('');
    onClose();
  };

  const handleCreate = async () => {
    if (!alias.trim()) return;

    setLoading(true);
    try {
      const result = await createTokenReq(alias, workspace);
      setToken(result);
      setState('result');
    } catch (error: any) {
      showToast({
        kind: 'negative',
        message: getErrorMessage(error) || 'Failed to create secret',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(token);
      showToast({
        kind: 'positive',
        message: 'Token copied to clipboard',
      });
    } catch (error) {
      showToast({
        kind: 'negative',
        message: 'Failed to copy token to clipboard',
      });
    }
  };

  return (
    <Modal isOpen={isOpen} onClose={handleClose}>
      <ModalHeader>Generate Database Token</ModalHeader>
      <ModalBody>
        <div className="flex flex-col gap-[16px]">
          <div>
            <div className={css({ marginBottom: '8px', fontWeight: 500, fontSize: '14px' })}>Secret Alias</div>
            <Input
              value={alias}
              onChange={(e) => setAlias(e.currentTarget.value)}
              placeholder="Enter secret alias"
              disabled={state === 'result'}
              clearable={state === 'input'}
            />
          </div>

          {state === 'result' && (
            <>
              <div>
                <div className={css({ marginBottom: '8px', fontWeight: 500 })}>Database Token</div>

                <div className="flex items-center">
                  <div className="flex-1">
                    <Input value={token} readOnly />
                  </div>
                  <Button kind="text" size="compact" onClick={handleCopy}>
                    <CopyIcon size={20} color={theme.colors['icon.primary']} />
                  </Button>
                </div>
              </div>

              <StyledToast
                kind={KIND.info}
                closeable={false}
                message="Please save this now as you won't be able to see it again."
              />
            </>
          )}
        </div>
      </ModalBody>
      <ModalFooter>
        {state === 'input' ? (
          <>
            <ModalButton kind="secondary" onClick={handleClose}>
              Cancel
            </ModalButton>
            <ModalButton onClick={handleCreate} disabled={!alias.trim() || loading} isLoading={loading}>
              {'Create'}
            </ModalButton>
          </>
        ) : (
          <ModalButton onClick={handleClose}>OK</ModalButton>
        )}
      </ModalFooter>
    </Modal>
  );
}
