import { useState, useEffect, useCallback, useMemo } from 'react';
import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import { Button } from '@tigergraph/app-ui-lib/button';
import { Drawer, DrawerBody, DrawerAction, DrawerHeader } from '@/components/Drawer';
import { showToast } from '@/components/styledToasterContainer';
import { GsqlQueryMeta, QueryParam, QueryParamType } from '@tigergraph/tools-models';
import { MdPlayArrow } from 'react-icons/md';
import { APICommand, QueryCommand } from '@/pages/editor/result/CommandExecutor';
import { useEditorContext } from '@/contexts/graphEditorContext';
import QueryConfig from '@/pages/editor/header/RunConfig';
import {
  buildQueryStrFromParamPayload,
  getDefalutPayloadByQueryParams,
  ParamError,
  ParamErrors,
} from '@/utils/queryParam';
import { ApiType } from '@/pages/editor/query/builtinEndpoints/type';
import { Tabs, Tab } from '@/components/Tab';
import { expand } from 'inline-style-expand-shorthand';
import { WorkspaceT } from '@/pages/workgroup/type';
import CodeSnippetTab from '@/pages/editor/query/tabs/CodeSnippetTab';
import BodyTab from '@/pages/editor/query/tabs/BodyTab';
import ParameterTab from '@/pages/editor/query/tabs/ParameterTab';
import { KIND } from 'baseui/button';

export interface RunAPIDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  type: 'query' | 'restpp endpoints';
  wp: WorkspaceT;
  graphName: string;
  name: string;
  // url path which begins at '/restpp';
  path: string;
  method: ApiType;
  pathParameters?: QueryParam[];
  bodyParameters: QueryParam[];
  query?: GsqlQueryMeta;
}

export default function RunAPIDrawer(props: RunAPIDrawerProps) {
  const { isOpen, onClose, wp, graphName, path, method, pathParameters, bodyParameters, type, query, name } = props;

  const [css, theme] = useStyletron();
  const { runCmd } = useEditorContext();

  const [activeTab, setActiveTab] = useState('parameter');

  const [copyContent, setCopyContent] = useState('');
  useEffect(() => {
    if (copyContent) {
      navigator.clipboard.writeText(copyContent);
      showToast({
        kind: 'positive',
        message: 'Copied to clipboard',
      });
    }
  }, [copyContent]);

  const [payload, setPayload] = useState<Record<string, any>>({});
  const [paramErrors, setParamErrors] = useState<ParamErrors>({});

  // Combine path and body parameters for unified handling
  const allParameters = useMemo(() => [...(pathParameters || []), ...bodyParameters], [pathParameters, bodyParameters]);

  useEffect(() => {
    const combinedPayload = {
      ...getDefalutPayloadByQueryParams(pathParameters || []),
      ...getDefalutPayloadByQueryParams(bodyParameters),
    };
    setPayload(combinedPayload);
  }, [pathParameters, bodyParameters]);

  const url = useMemo(() => {
    let modifiedPath = path.replace(/{([^}]+)}/g, function (match, content) {
      const value = payload[content];
      return value;
    });

    if (method === ApiType.GET) {
      const queryStr = buildQueryStrFromParamPayload(graphName, bodyParameters, payload);
      modifiedPath += queryStr ? `?${queryStr}` : '';
    }

    return `https://${wp.nginx_host}${modifiedPath}`;
  }, [path, method, wp.nginx_host, payload, graphName, bodyParameters]);

  const { memoryLimit, timeLimit, profile } = useEditorContext();

  const validateSimpleTypeInput = (paramType: QueryParamType, value: any): string => {
    if (paramType.type === 'INT') {
      if (!/^-?\d+$/.test(value)) {
        return 'Please input a valid INT.';
      }
    }

    if (paramType.type === 'UINT') {
      if (!/^\d+$/.test(value)) {
        return 'Please input a valid UINT.';
      }
    }

    if (paramType.type === 'FLOAT' || paramType.type === 'DOUBLE') {
      if (isNaN(Number(value)) || value === '') {
        return `Please input a valid ${paramType.type}.`;
      }
    }

    if (paramType.type === 'VERTEX') {
      if (!value?.id) {
        return 'Please input a valid vertex ID.';
      }
    }

    if (paramType.type === 'DATETIME') {
      if ((isNaN(Number(value)) && isNaN(Date.parse(value))) || value === '') {
        return 'Please input a valid DATETIME.';
      }
    }

    return '';
  };

  const validateInput = useCallback((param: QueryParam, value: any): { error: ParamError; hasError: boolean } => {
    const type = param.paramType.type;
    let hasError = false;
    let error: ParamError = '';
    if (type === 'LIST') {
      error = value.map((item: any) => validateSimpleTypeInput(param.paramType.elementType, item));
      hasError = (error as string[]).some((err: string) => !!err);
    } else if (type === 'MAP') {
      error = value.map(({ key, value }: { key: any; value: any }) => ({
        key: validateSimpleTypeInput(param.paramType.keyType, key),
        value: validateSimpleTypeInput(param.paramType.valueType, value),
      }));
      hasError = (error as { key: string; value: string }[]).some(({ key, value }) => !!key || !!value);
    } else {
      error = validateSimpleTypeInput(param.paramType, value);
      hasError = !!error;
    }

    return { error, hasError };
  }, []);

  const validateAllInputs = useCallback(
    (queryPayload: Record<string, any>) => {
      const errors: ParamErrors = {};
      let hasError = false;
      for (const param of allParameters) {
        const value = queryPayload[param.paramName];
        const { error, hasError: _hasError } = validateInput(param, value);
        errors[param.paramName] = error;
        hasError = hasError || _hasError;
      }
      setParamErrors(errors);

      return hasError;
    },
    [allParameters, validateInput]
  );

  const onParamChange = useCallback(
    (param: QueryParam, value: any) => {
      setPayload((prev) => ({ ...prev, [param.paramName]: value }));

      const { error } = validateInput(param, value);
      setParamErrors((prev) => ({ ...prev, [param.paramName]: error }));
    },
    [validateInput]
  );

  const onRunClick = () => {
    const hasParamError = validateAllInputs(payload);
    if (hasParamError) {
      if (activeTab !== 'parameter') {
        setActiveTab('parameter');
      }
      return;
    }

    if (type === 'query') {
      runCmd(
        wp,
        graphName,
        new QueryCommand(
          wp.workspace_id,
          graphName,
          query!,
          payload,
          {
            memoryLimit,
            timeLimit,
            profile,
          },
          {
            nginx_host: wp.nginx_host,
            tg_version: wp.tg_version,
          }
        )
      );
    } else {
      const modifiedPath = path.replace(/{([^}]+)}/g, function (match, content) {
        const value = payload[content];
        return value;
      });
      const headers: Record<string, string> = {};
      // only query supports memory limit and timeout
      if (path.startsWith('/restpp/query')) {
        if (Number(memoryLimit)) {
          headers['GSQL-QueryLocalMemLimitMB'] = `${memoryLimit}`;
        }
        if (Number(timeLimit)) {
          headers['Gsql-Timeout'] = `${Number(timeLimit) * 1000}`;
        }
      }
      runCmd(
        wp,
        graphName,
        new APICommand(
          wp.workspace_id,
          graphName,
          {
            path: modifiedPath,
            method,
            payload,
            params: bodyParameters,
            headers,
          },
          wp
        )
      );
    }
    onClose();
  };

  return (
    <Drawer isOpen={isOpen} onClose={onClose} size="auto">
      <DrawerHeader>
        <div className="flex items-center gap-[8px]">
          <MdPlayArrow size={24} />
          <div className="w-[500px] truncate">
            {`Run ${type === 'query' ? 'Query' : 'API'}`} : {name}
          </div>
        </div>
      </DrawerHeader>
      <DrawerBody $style={{ ...expand({ padding: '0' }) }}>
        <div className="flex flex-col gap-[12px] h-full">
          <Tabs
            activeKey={activeTab}
            onChange={({ activeKey }) => setActiveTab(activeKey as string)}
            overrides={{
              TabList: {
                style: {
                  backgroundColor: theme.colors['background.primary'],
                },
              },
            }}
          >
            <Tab
              title="Parameters"
              key="parameter"
              overrides={{
                TabPanel: {
                  style: {
                    height: '100%',
                    ...expand({ padding: '12px 16px' }),
                  },
                },
              }}
            >
              <ParameterTab
                type={type}
                allParameters={allParameters}
                payload={payload}
                onParamChange={onParamChange}
                graphName={graphName}
                paramErrors={paramErrors}
                query={query}
              />
            </Tab>

            <Tab
              title="JSON"
              key="body"
              overrides={{
                TabPanel: {
                  style: {
                    height: '100%',
                    ...expand({ padding: '12px 16px' }),
                  },
                },
              }}
            >
              <BodyTab parameters={bodyParameters} payload={payload} setCopyContent={setCopyContent} />
            </Tab>

            <Tab
              title="Code Snippet"
              key="code-snippet"
              overrides={{
                TabPanel: {
                  style: {
                    height: '100%',
                    ...expand({ padding: '12px 16px' }),
                  },
                },
              }}
            >
              <CodeSnippetTab
                type={type}
                query={query}
                method={method}
                url={url}
                payload={payload}
                parameters={bodyParameters}
                setCopyContent={setCopyContent}
                wp={wp}
              />
            </Tab>
          </Tabs>
        </div>
      </DrawerBody>
      <DrawerAction>
        <div className={css({ display: 'flex', gap: '8px', justifyContent: 'flex-end' })}>
          {/* only query supports memory limit and timeout */}
          {(type === 'query' || path.startsWith('/restpp/query')) && (
            <QueryConfig canDoProfile={!!query?.installed && query.installMode !== 'UDF'} />
          )}
          {type === 'restpp endpoints' && (
            <Button kind={KIND.secondary} onClick={onClose}>
              Back
            </Button>
          )}
          <Button overrides={{ BaseButton: { style: { height: '30px' } } }} size="compact" onClick={onRunClick}>
            Run
          </Button>
        </div>
      </DrawerAction>
    </Drawer>
  );
}
