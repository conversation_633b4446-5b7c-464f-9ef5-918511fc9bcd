import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import { mergeOverrides } from 'baseui';
import { Tabs, TabsProps } from 'baseui/tabs-motion';

export function SnippetsTabs({ overrides, ...props }: TabsProps) {
  const [css, theme] = useStyletron();

  return (
    <Tabs
      overrides={mergeOverrides(
        {
          Root: {
            style: {
              height: '100%',
              transform: 'none', // to fix codemirror autocomplete tooltip position, see https://github.com/codemirror/dev/issues/324
            },
          },
          TabHighlight: {
            style: {
              height: '2px',
              backgroundColor: theme.colors['input.background.disabled'],
            },
          },
          TabBorder: {
            style: {
              height: '1px',
              backgroundColor: `${theme.colors.divider}`,
              marginTop: '-1px',
            },
          },
          TabList: {
            style: {
              paddingTop: '4px',
              height: '28px',
              paddingBottom: '0px',
              marginBottom: '0px',
              width: 'calc(100% - 180px)',
              overflowX: 'auto',
            },
          },
        },
        overrides as any
      )}
      {...props}
    />
  );
}
