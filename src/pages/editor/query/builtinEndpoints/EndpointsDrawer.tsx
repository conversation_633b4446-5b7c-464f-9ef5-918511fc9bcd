import { <PERSON>er, Drawer<PERSON><PERSON>, DrawerHeader } from '@/components/Drawer.tsx';
import { Tab, Tabs } from '@/components/Tab.tsx';
import { axiosCluster } from '@/lib/network.ts';
import { GraphSelector } from '@/pages/explore/explore/GraphSelector.tsx';
import { Endpoint } from '@/pages/editor/query/builtinEndpoints/Endpoint';
import { WorkspaceT } from '@/pages/workgroup/type.ts';
import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import { Input } from '@tigergraph/app-ui-lib/input';
import { Key, useEffect, useState } from 'react';
import { useQuery } from 'react-query';
import { DrawerHeadContainer } from '@/lib/styled.tsx';
import { LinkIcon } from '@/pages/home/<USER>';
import { BaseSkeleton } from '@/components/BaseSkeleton.tsx';
import { MdRefresh } from 'react-icons/md';
import { But<PERSON> } from '@tigergraph/app-ui-lib/button';
import { ID_TOKEN_KEY } from '@/contexts/workspaceContext';
import { fetchGraphList } from '@/lib/instance_api';
import { GsqlParameter, QueryMetaLogic, QueryParam } from '@tigergraph/tools-models';
import RunAPIDrawer from '@/pages/editor/query/RunAPIDrawer';
import { ApiType } from '@/pages/editor/query/builtinEndpoints/type';

export const ReadyApi = [
  'DELETE /graph/{graph_name}/delete_by_type/vertices/{vertex_type}/',
  'DELETE /graph/{graph_name}/edges/{source_vertex_type}/{source_vertex_id}/{edge_type}/{target_vertex_type}/{target_vertex_id}',
  'DELETE /graph/{graph_name}/edges/{source_vertex_type}/{source_vertex_id}/{edge_type}/{target_vertex_type}/{target_vertex_id}/{discriminator}',
  'DELETE /graph/{graph_name}/vertices/{vertex_type}/{vertex_id}',
  'GET /abortquery/{graph_name}',
  'GET /data_consistency_check',
  'GET /deleted_vertex_check/{graph_name}',
  'GET /endpoints/{graph_name}',
  'GET /graph/{graph_name}/edges/{source_vertex_type}/{source_vertex_id}/{edge_type}/{target_vertex_type}/{target_vertex_id}',
  'GET /graph/{graph_name}/edges/{source_vertex_type}/{source_vertex_id}/{edge_type}/{target_vertex_type}/{target_vertex_id}/{discriminator}',
  'GET /graph/{graph_name}/vertices/{vertex_type}/{vertex_id}',
  'GET /query_result/{requestid}',
  'GET /rebuildnow/{graph_name}',
  'GET /showdelayedlistall',
  'GET /showprocesslist/{graph_name}',
  'GET /showprocesslistall',
  'GET /statistics/{graph_name}',
  'POST /ddl/{graph_name}',
];

interface EndpointsDrawerProps {
  isOpen: boolean;
  workspace: WorkspaceT;
  onClose: () => void;
}

export function EndpointsDrawer({ isOpen, workspace, onClose }: EndpointsDrawerProps) {
  const baseurl = `https://${workspace.nginx_host}`;

  const [css, theme] = useStyletron();
  const [graph, setGraph] = useState<string>('');
  const [customEndpoints, setCustomEndpoints] = useState<Endpoints | null>(null);
  const [activeKey, setActiveKey] = useState<Key>('Installed Query');
  const [searchInput, setSearchInput] = useState<string>('');

  // RunAPIDrawer state
  const [isRunDrawerOpen, setIsRunDrawerOpen] = useState(false);
  const [runDrawerProps, setRunDrawerProps] = useState<{
    path: string;
    method: ApiType;
    graphName: string;
    pathParameters: QueryParam[];
    bodyParameters: QueryParam[];
    name: string;
  } | null>(null);

  const catalogQueryClient = useQuery(
    ['getCatalog', workspace.nginx_host, graph],
    async () => {
      const res = await axiosCluster.get(`${baseurl}/api/restpp/endpoints`, { params: { graph } });
      return res.data;
    },
    {
      enabled: graph !== '',
    }
  );
  const { refetch: refetchCatalog } = catalogQueryClient;

  const builtInQueryClient = useQuery(
    ['getInstalledQuery', workspace.nginx_host, graph],
    async () => {
      const res = await axiosCluster.get(`${baseurl}/api/restpp/endpoints`, {
        params: { builtin: true, graph },
      });
      return res.data;
    },
    {
      enabled: graph !== '',
    }
  );
  const { refetch: refetchBuiltIn } = builtInQueryClient;

  const handleClickRefresh = () => {
    refetchCatalog();
    refetchBuiltIn();
  };

  // filter custom endpoints using builtInEndpoints and allEndpoints
  useEffect(() => {
    const allEndpoints = catalogQueryClient.data?.results;
    const builtInEndpoints = builtInQueryClient.data?.results;

    // filter custom endpoints using builtInEndpoints and allEndpoints
    if (builtInEndpoints && allEndpoints) {
      let customEndpoints: Endpoints = {};
      for (let key in allEndpoints) {
        if (!builtInEndpoints[key] && allEndpoints[key].enabled && key.includes(graph)) {
          customEndpoints[key] = allEndpoints[key];
        }
      }
      setCustomEndpoints(customEndpoints);
    }
  }, [catalogQueryClient.data, builtInQueryClient.data, graph]);

  const simpleAuthClient = useQuery(
    ['graph_list', workspace?.nginx_host],
    async () => {
      if (!workspace) {
        return [];
      }

      return fetchGraphList(workspace?.nginx_host, workspace.tg_version, sessionStorage.getItem(ID_TOKEN_KEY)!);
    },
    {
      enabled: isOpen,
      onSuccess(data) {
        // setup default graph name
        if (data.length > 0) {
          const defaultGraph = data[0];
          setGraph(defaultGraph);
        }
      },
    }
  );

  // Calculate parameters for RunAPIDrawer (moved from Endpoint component)
  const calculateRunDrawerParams = (apiName: string, apiInfo: Record<string, GsqlParameter>, graphName: string) => {
    const pattern = /{([^}]+)}/g;
    const matches = apiName.split(' ')[1].matchAll(pattern);
    let pathParameters: QueryParam[] = [];
    for (const match of matches) {
      pathParameters.push({
        paramName: match[1],
        paramType: { type: 'STRING' },
        paramDefaultValue: match[1] === 'graph_name' ? graphName : '',
      });
    }

    // the parameters in apiInfo.parameters except path parameters are query parameters
    const filteredParams: Record<string, GsqlParameter> = {};
    let bodyParameters: QueryParam[] = [];
    if (apiInfo) {
      for (const param of Object.keys(apiInfo)) {
        // for a same query, restpp will add a read_commited parameter which doesn't exist in gsql response
        // we filter out it to make them consistent
        if (!pathParameters.find((p) => p.paramName === param) && param !== 'read_committed') {
          filteredParams[param] = apiInfo[param];
        }
      }
      bodyParameters = QueryMetaLogic.convertGSQLParameters(filteredParams);
    }

    return {
      pathParameters,
      bodyParameters,
      path: `/restpp${apiName.split(' ')[1]}`,
      method: apiName.split(' ')[0] as ApiType,
      name: apiName.split(' ')[1],
      graphName,
    };
  };

  // Handle endpoint click
  const handleEndpointClick = (apiName: string, apiInfo: Record<string, GsqlParameter>, graphName: string) => {
    setRunDrawerProps(calculateRunDrawerParams(apiName, apiInfo, graphName));
    setIsRunDrawerOpen(true);
  };

  type Endpoints = Record<string, { parameters: Record<string, GsqlParameter> }>;

  const tabContent = (endpoints: Endpoints, searchInput: string, readyApi: string[]) => {
    let filteredEndpoints = Object.keys(endpoints)
      .filter((item) => {
        if (!searchInput) return true;
        const search = searchInput.trim().toLowerCase();
        return item.toLowerCase().includes(search);
      })
      .filter((item) => {
        if (readyApi.length === 0) return true;
        return readyApi.includes(item);
      })
      .filter((item) => {
        if (!workspace.is_rw) {
          const apiType = item.split(' ')[0];
          return apiType === 'GET';
        }
        return true;
      })
      .sort((a, b) => -+(a > b));

    const isFetching = catalogQueryClient.isFetching || builtInQueryClient.isFetching || simpleAuthClient.isFetching;
    const isFetched = catalogQueryClient.isFetched && builtInQueryClient.isFetched && simpleAuthClient.isFetched;

    return (
      <>
        <div
          className={css({
            display: 'flex',
            justifyContent: 'space-between',
          })}
        >
          <div
            className={css({
              width: '28%',
              marginBottom: '16px',
            })}
          >
            <GraphSelector
              graphs={simpleAuthClient.data || []}
              graph={graph}
              isFetching={false}
              onGraphChanged={(graphName) => {
                setGraph(graphName);
              }}
            />
          </div>
          <Input
            value={searchInput}
            overrides={{
              Root: {
                style: {
                  width: '70%',
                  marginBottom: '16px',
                  height: '34px',
                },
              },
              Input: {
                style: {
                  fontSize: '14px',
                },
              },
            }}
            placeholder="Search"
            onChange={(e) => setSearchInput(e.currentTarget.value)}
          />
        </div>
        <div>
          {isFetching ? (
            <div
              className={css({
                display: 'flex',
                flexDirection: 'column',
                gap: '10px',
              })}
            >
              <BaseSkeleton height={'40px'} />
              <BaseSkeleton height={'40px'} />
              <BaseSkeleton height={'40px'} />
              <BaseSkeleton height={'40px'} />
              <BaseSkeleton height={'40px'} />
              <BaseSkeleton height={'40px'} />
              <BaseSkeleton height={'40px'} />
              <BaseSkeleton height={'40px'} />
            </div>
          ) : (
            <>
              {isFetched && filteredEndpoints.length === 0 && <div>No endpoints available</div>}
              {isFetched && !graph && <div>Graph schema is empty, please create graph schema.</div>}
              {isFetched && graph && filteredEndpoints.length > 0 && (
                <div>
                  {filteredEndpoints.map((item) => {
                    return (
                      <Endpoint
                        key={item}
                        apiName={item}
                        apiInfo={endpoints[item]['parameters']}
                        graphName={graph}
                        onClick={() => handleEndpointClick(item, endpoints[item]['parameters'], graph)}
                      />
                    );
                  })}
                </div>
              )}
            </>
          )}
        </div>
      </>
    );
  };

  return (
    <>
      <Drawer isOpen={isOpen} onClose={onClose}>
        <DrawerHeader>
          <DrawerHeadContainer>
            <LinkIcon />
            Connect From API
          </DrawerHeadContainer>
        </DrawerHeader>
        <DrawerBody
          $style={{
            paddingLeft: '0px',
            paddingRight: '0px',
            paddingTop: '0px',
            paddingBottom: '0px',
            overflow: 'hidden',
            position: 'relative',
          }}
        >
          <Tabs
            activeKey={activeKey}
            onChange={({ activeKey }) => {
              setActiveKey(activeKey as Key);
            }}
            overrides={{
              TabList: {
                style: {
                  backgroundColor: theme.colors['background.primary'],
                },
              },
            }}
          >
            <Tab
              overrides={{
                Tab: {
                  style: {
                    ':focus-visible': 'none',
                  },
                },
                TabPanel: {
                  style: {
                    height: 'calc(100vh - 100px)',
                    minHeight: 'calc(100vh - 100px)',
                    overflow: 'auto',
                    paddingLeft: '16px',
                    paddingRight: '16px',
                  },
                },
              }}
              title={'Installed Query'}
              key={'Installed Query'}
            >
              {tabContent(customEndpoints || {}, searchInput, [])}
            </Tab>
            <Tab
              overrides={{
                TabPanel: {
                  style: {
                    height: 'calc(100vh - 100px)',
                    minHeight: 'calc(100vh - 100px)',
                    overflow: 'auto',
                    paddingLeft: '16px',
                    paddingRight: '16px',
                  },
                },
              }}
              title={'Built-In Query'}
              key={'Built-In Query'}
            >
              {tabContent(builtInQueryClient.data?.results || {}, searchInput, ReadyApi)}
            </Tab>
          </Tabs>
          <div className={css({ position: 'absolute', top: '4px', right: '18px' })}>
            <Button kind="text" shape="square" size="large" onClick={handleClickRefresh}>
              <MdRefresh size={24} />
            </Button>
          </div>
        </DrawerBody>
      </Drawer>

      {runDrawerProps && (
        <RunAPIDrawer
          isOpen={isRunDrawerOpen}
          onClose={() => setIsRunDrawerOpen(false)}
          type="restpp endpoints"
          wp={workspace}
          {...runDrawerProps!}
        />
      )}
    </>
  );
}
