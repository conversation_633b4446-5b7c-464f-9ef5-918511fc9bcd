import { ListItem, ListItemLabel } from '@/components/Expandable';
import { EdgeIcon, ExploreIcon2, VertexIcon } from '@/pages/home/<USER>';
import { Button } from '@tigergraph/app-ui-lib/button';
import { useNavigate } from 'react-router-dom';

export interface SchemaListItemProps {
  name: string;
  type: 'vertex' | 'edge';
  onClick: () => void;
}

export function SchemaListItem({ name, type, onClick }: SchemaListItemProps) {
  const navigate = useNavigate();

  const handleExploreClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    navigate('/explore', { state: { defaultVertexEdgeType: name } });
  };

  const icon = type === 'vertex' ? <VertexIcon /> : <EdgeIcon />;

  return (
    <ListItem key={name} onClick={onClick}>
      <>
        <div className='flex-1'>
          <ListItemLabel icon={icon} label={name} />
        </div>
        <Button 
          size="compact" 
          kind="text" 
          shape="square" 
          onClick={handleExploreClick}
        >
          <ExploreIcon2 />
        </Button>
      </>
    </ListItem>
  );
}
