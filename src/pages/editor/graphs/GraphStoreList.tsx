import React, { useState, useEffect } from 'react';
import { WorkspaceT } from '@/pages/workgroup/type';
import GraphStore from '@/pages/editor/graphs/GraphStore';
import { CreateTempFileFn } from '@/pages/editor/file/hooks';
import { ExternalLink, ExternalNode, GLOBAL_GRAPH_NAME } from '@tigergraph/tools-models';
import { LinkIcon } from '@/pages/home/<USER>';
import { Input } from '@tigergraph/app-ui-lib/input';
import searchIcon from '@/assets/search.svg';
import { Button } from '@tigergraph/app-ui-lib/button';
import { EndpointsDrawer } from '@/pages/editor/query/builtinEndpoints/EndpointsDrawer';
import { MdOutlineRefresh } from 'react-icons/md';
import { useQueryClient } from 'react-query';
import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import { useGraphExpanded } from '@/contexts/graphExpandedContext';

interface GraphStoreListProps {
  graphNames: string[];
  currentWorkspace: WorkspaceT;
  onSchemaItemSelect: (item: ExternalNode | ExternalLink, graphName: string) => void;
  createTempFile: CreateTempFileFn;
}

const GraphStoreList: React.FC<GraphStoreListProps> = ({
  graphNames,
  currentWorkspace,
  onSchemaItemSelect,
  createTempFile,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchText, setSearchText] = useState('');
  const queryClient = useQueryClient();
  const [css, theme] = useStyletron();
  const { ensureFirstGraphExpanded } = useGraphExpanded();

  // Ensure the first graph is expanded when the component mounts or graphNames change
  useEffect(() => {
    ensureFirstGraphExpanded(graphNames);
  }, [graphNames, ensureFirstGraphExpanded]);

  const handleSearchTextChange = (text: string) => {
    setSearchText(text);
  };

  const handleRefresh = () => {
    // Invalidate all schema and query data for all graphs
    graphNames.forEach((graphName) => {
      if (graphName !== GLOBAL_GRAPH_NAME) {
        // Invalidate schema queries
        queryClient.resetQueries(['schema', graphName, currentWorkspace?.nginx_host, currentWorkspace?.tg_version]);
        // Invalidate query queries
        queryClient.resetQueries(['queries', currentWorkspace?.workspace_id, graphName]);
      }
    });
  };

  return (
    <>
      <div className="flex items-center justify-between p-[8px] gap-[8px]">
        <Input
          overrides={{
            Root: {
              style: {
                borderTopWidth: '1px',
                borderRightWidth: '1px',
                borderBottomWidth: '1px',
                borderLeftWidth: '1px',
              },
            },
            Input: {
              style: {
                height: '32px',
                paddingLeft: '8px',
              },
            },
          }}
          onChange={(e) => handleSearchTextChange(e.currentTarget.value)}
          onKeyDown={(e) => {
            e.stopPropagation();
          }}
          placeholder="Search..."
          startEnhancer={() => <img src={searchIcon} />}
        />
        <div className="flex items-center gap-[4px]">
          <Button size="compact" kind="text" shape="square" onClick={handleRefresh}>
            <MdOutlineRefresh size={20} color={theme.colors['button.icon']} />
          </Button>
          <Button size="compact" kind="text" shape="square" onClick={() => setIsOpen(true)}>
            <LinkIcon />
          </Button>
        </div>
      </div>
      {graphNames.map((graphName) =>
        graphName === GLOBAL_GRAPH_NAME ? null : (
          <GraphStore
            key={graphName}
            wp={currentWorkspace}
            graphName={graphName}
            searchText={searchText}
            onSchemaItemSelect={onSchemaItemSelect}
            createTempFile={createTempFile}
          />
        )
      )}
      <EndpointsDrawer
        isOpen={isOpen}
        workspace={currentWorkspace}
        onClose={() => {
          setIsOpen(false);
        }}
      />
    </>
  );
};

export default GraphStoreList;
