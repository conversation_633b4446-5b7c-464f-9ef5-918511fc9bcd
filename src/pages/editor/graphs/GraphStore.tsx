import { Expandable, ListItemLabel } from '@/components/Expandable';
import { SchemaList } from './SchemaList';
import { WorkspaceT } from '@/pages/workgroup/type';
import { ExternalLink, ExternalNode } from '@tigergraph/tools-models';
import QueryList from '@/pages/editor/graphs/QueryList';
import { CreateTempFileFn } from '@/pages/editor/file/hooks';
import { GraphIcon } from '@/pages/home/<USER>';
import { useGraphExpanded } from '@/contexts/graphExpandedContext';

export interface GraphStoreProps {
  wp: WorkspaceT;
  graphName: string;
  searchText?: string;
  onSchemaItemSelect: (item: ExternalNode | ExternalLink, graphName: string) => void;
  createTempFile: CreateTempFileFn;
}

export default function GraphStore({ wp, graphName, searchText, onSchemaItemSelect, createTempFile }: GraphStoreProps) {
  const { getGraphStoreExpanded, setGraphStoreExpanded } = useGraphExpanded();
  const expanded = getGraphStoreExpanded(graphName);

  return (
    <>
      <Expandable
        label={<ListItemLabel icon={<GraphIcon />} label={graphName} />}
        expanded={expanded}
        onExpandChange={(newExpanded) => setGraphStoreExpanded(graphName, newExpanded)}
      >
        <SchemaList
          wp={wp}
          graphName={graphName}
          expanded={expanded}
          searchText={searchText}
          onSchemaItemSelect={onSchemaItemSelect}
        />
        <QueryList
          wp={wp}
          graphName={graphName}
          expanded={expanded}
          searchText={searchText}
          createTempFile={createTempFile}
        />
      </Expandable>
    </>
  );
}
