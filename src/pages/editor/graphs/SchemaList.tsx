import { useMemo } from 'react';
import { Expandable, ListItem, ListItemLabel } from '@/components/Expandable';
import { ExternalLink, ExternalNode } from '@tigergraph/tools-models';
import { convertSchemaToGraph } from '@tigergraph/tools-ui/esm/graph/data';
import { DatabaseIcon, EdgeIcon, ExploreIcon2, VertexIcon } from '@/pages/home/<USER>';
import { WorkspaceT } from '@/pages/workgroup/type';
import { emptySchema, useSchema } from '@/utils/useSchema';
import { LoadingIndicator } from '@/components/loading-indicator';
import { useGraphExpanded } from '@/contexts/graphExpandedContext';
import { Button } from '@tigergraph/app-ui-lib/button';
import { useNavigate } from 'react-router-dom';

export interface SchemaListProps {
  wp: WorkspaceT;
  graphName: string;
  expanded: boolean;
  searchText?: string;
  onSchemaItemSelect: (item: ExternalNode | ExternalLink, graphName: string) => void;
}

export function SchemaList({ wp, graphName, expanded, searchText = '', onSchemaItemSelect }: SchemaListProps) {
  const navigate = useNavigate();

  const { getSchemaListExpanded, setSchemaListExpanded } = useGraphExpanded();
  const localExpanded = getSchemaListExpanded(graphName);

  // Fetch schema when expanded
  const { data: schema = emptySchema, isLoading } = useSchema(wp, graphName, localExpanded);

  const vertices = schema.VertexTypes;
  const edges = schema.EdgeTypes;
  const schemaGraph = useMemo(() => {
    return convertSchemaToGraph(schema);
  }, [schema]);

  // Filter schema items based on search text
  const filteredVertices = vertices.filter(({ Name }) => Name.toLowerCase().includes(searchText.toLowerCase()));
  const filteredEdges = edges.filter(({ Name }) => Name.toLowerCase().includes(searchText.toLowerCase()));

  const handleClickSchemaItem = (name: string) => {
    const item =
      schemaGraph.nodes.find((node) => node.type === name) || schemaGraph.links.find((link) => link.type === name);
    if (item) {
      onSchemaItemSelect(item, schema.GraphName);
    }
  };

  return (
    <Expandable
      label={<ListItemLabel icon={<DatabaseIcon />} label={'Schema'} />}
      expanded={localExpanded}
      onExpandChange={(newExpanded) => setSchemaListExpanded(graphName, newExpanded)}
    >
      {isLoading ? (
        <LoadingIndicator />
      ) : (
        <>
          {filteredVertices.map(({ Name }) => (
            <ListItem key={Name} onClick={() => handleClickSchemaItem(Name)}>
              <>
              <div className='flex-1'>
               <ListItemLabel icon={<VertexIcon />} label={Name} />
              </div>
               <Button size="compact" kind="text" shape="square" onClick={() => { navigate('/explore', { state: { defaultVertexEdgeType: Name } }); }}><ExploreIcon2 /></Button>
              </>
            </ListItem>
          ))}
          {filteredEdges.map(({ Name }) => (
            <ListItem key={Name} onClick={() => handleClickSchemaItem(Name)}>
              <>
                <ListItemLabel icon={<EdgeIcon />} label={Name} />
                <Button size="compact" kind="text" shape="square" onClick={() => { navigate('/explore', { state: { defaultVertexEdgeType: Name } }); }}><ExploreIcon2 /></Button>
              </>
            </ListItem>
          ))}
        </>
      )}
    </Expandable>
  );
}
