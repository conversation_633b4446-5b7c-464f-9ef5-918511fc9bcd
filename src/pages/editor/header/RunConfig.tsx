import { CustomTheme, useStyletron } from '@tigergraph/app-ui-lib/Theme';

import { MdOutlineTimer } from 'react-icons/md';
import { MdOutlineSdCard } from 'react-icons/md';
// import ProfileIcon from '@/assets/profile.svg?react';
import { Button } from '@tigergraph/app-ui-lib/button';
import { StatefulPopover } from '@tigergraph/app-ui-lib/popover';
import { TRIGGER_TYPE, PLACEMENT, Popover, PopoverOverrides } from 'baseui/popover';
import { useState } from 'react';
import { Input } from '@tigergraph/app-ui-lib/input';
import { useEditorContext } from '@/contexts/graphEditorContext';
import { useWorkspaceContext } from '@/contexts/workspaceContext';
import { supportQueryProfile } from '@/utils/supportFeature';
import { IoSpeedometerOutline } from 'react-icons/io5';

export default function QueryConfig({ canDoProfile }: { canDoProfile: boolean }) {
  const [css, theme] = useStyletron();
  const [showTimeout, setShowTimeout] = useState(false);
  const [showMemoryLimit, setShowMemoryLimit] = useState(false);

  const { memoryLimit, setMemoryLimit, timeLimit, setTimeLimit, profile, setProfile } = useEditorContext();
  const { currentWorkspace } = useWorkspaceContext();

  const onCloseTimeout = () => {
    setTimeout(() => {
      setShowTimeout(false);
    }, 200);
  };

  const onCloseMemoryLimit = () => {
    setTimeout(() => {
      setShowMemoryLimit(false);
    }, 50);
  };

  return (
    <div
      className={css({
        display: 'flex',
        borderRadius: '2px',
        border: `1px solid ${theme.colors['border.tertiary']}`,
        padding: '4px',
        gap: '4px',
        marginRight: '8px',
        position: 'relative',
      })}
    >
      <StatefulPopover
        triggerType={TRIGGER_TYPE.hover}
        content={`Timeout ${timeLimit > 0 ? 'Enabled' : 'Disabled'}`}
        placement={PLACEMENT.bottomRight}
      >
        <Button
          kind="text"
          size="compact"
          shape="square"
          onClick={() => setShowTimeout((prev) => !prev)}
          overrides={
            timeLimit > 0
              ? {
                  BaseButton: {
                    style: {
                      backgroundColor: theme.colors['button.background.default.selected'],
                      ':hover': {
                        backgroundColor: theme.colors['button.background.default.selected'],
                      },
                    },
                  },
                }
              : {}
          }
        >
          <MdOutlineTimer
            size={16}
            color={timeLimit > 0 ? theme.colors['button.icon.inverse'] : theme.colors['button.icon']}
          />
        </Button>
      </StatefulPopover>
      <StatefulPopover
        triggerType={TRIGGER_TYPE.hover}
        content={`Memory Limit ${memoryLimit > 0 ? 'Enabled' : 'Disabled'}`}
        placement={PLACEMENT.bottomRight}
      >
        <Button
          kind="text"
          size="compact"
          shape="square"
          onClick={() => setShowMemoryLimit((prev) => !prev)}
          overrides={
            memoryLimit > 0
              ? {
                  BaseButton: {
                    style: {
                      backgroundColor: theme.colors['button.background.default.selected'],
                      ':hover': {
                        backgroundColor: theme.colors['button.background.default.selected'],
                      },
                    },
                  },
                }
              : {}
          }
        >
          <MdOutlineSdCard
            size={16}
            color={memoryLimit > 0 ? theme.colors['button.icon.inverse'] : theme.colors['button.icon']}
          />
        </Button>
      </StatefulPopover>
      {supportQueryProfile(currentWorkspace) && (
        <StatefulPopover
          triggerType={TRIGGER_TYPE.hover}
          content={
            canDoProfile ? (
              <>{`Query Profile ${profile ? 'Enabled' : 'Disabled'}`}</>
            ) : (
              <span
                className={css({
                  whiteSpace: 'pre-wrap',
                })}
              >{`Query profiling data is only available for installed GSQL syntax queries in GPR-mode (both single and distributed).\nIf you are installing the query via the front-end web, it will automatically add the -single flag for non-distributed query.\nIf you are installing the query using the gsql command on the back-end, you can use either the "distributed" or "-single" flag.`}</span>
            )
          }
          placement={PLACEMENT.bottomLeft}
        >
          <Button
            kind="text"
            size="compact"
            shape="square"
            onClick={() => {
              if (canDoProfile) {
                setProfile((prev) => !prev);
              }
            }}
            overrides={
              profile && canDoProfile
                ? {
                    BaseButton: {
                      style: {
                        backgroundColor: theme.colors['button.background.default.selected'],
                        ':hover': {
                          backgroundColor: theme.colors['button.background.default.selected'],
                        },
                      },
                    },
                  }
                : {}
            }
          >
            <IoSpeedometerOutline
              size={16}
              color={
                canDoProfile
                  ? profile
                    ? theme.colors['button.icon.inverse']
                    : theme.colors['button.icon']
                  : theme.colors['button.icon.disabled']
              }
            />
          </Button>
        </StatefulPopover>
      )}
      <Popover
        isOpen={showTimeout}
        onClickOutside={onCloseTimeout}
        onEsc={onCloseTimeout}
        placement={PLACEMENT.topRight}
        overrides={popOverrides}
        content={
          <ConfigForm value={timeLimit} onValueChange={setTimeLimit} name="timeout" description="Timeout (seconds)" />
        }
      >
        <span
          className={css({
            position: 'absolute',
            bottom: 0,
            right: '32px',
          })}
        />
      </Popover>
      <Popover
        isOpen={showMemoryLimit}
        onClickOutside={onCloseMemoryLimit}
        onEsc={onCloseMemoryLimit}
        placement={PLACEMENT.topRight}
        overrides={popOverrides}
        content={
          <ConfigForm
            value={memoryLimit}
            onValueChange={setMemoryLimit}
            name="memory"
            description="Memory Limit (MBs)"
          />
        }
      >
        <span
          className={css({
            position: 'absolute',
            bottom: 0,
            right: '32px',
          })}
        />
      </Popover>
    </div>
  );
}

function ConfigForm({
  name,
  description,
  value,
  onValueChange,
}: {
  name: string;
  description: string;
  value: number;
  onValueChange: (value: number) => void;
}) {
  const [css, theme] = useStyletron();

  return (
    <div>
      <div
        className={css({
          fontSize: '12px',
          lineHeight: '16px',
          fontWeight: 500,
          color: theme.colors['input.text'],
          marginBottom: '4px',
        })}
      >
        {description}
      </div>
      <div
        className={css({
          display: 'flex',
          gap: '4px',
        })}
      >
        <Input
          type="number"
          min={0}
          size="compact"
          name={name}
          aria-label={description}
          value={`${value}`}
          onChange={(e) => onValueChange(Number(e.currentTarget.value))}
        />
        <Button
          kind="link"
          size="compact"
          overrides={{
            BaseButton: {
              style: {
                ':hover': {
                  textDecorationLine: 'none',
                },
                ':active': {
                  textDecorationLine: 'none',
                },
              },
            },
          }}
          onClick={() => {
            onValueChange(0);
          }}
        >
          Clear
        </Button>
      </div>
    </div>
  );
}

const popOverrides: PopoverOverrides = {
  Body: {
    style: ({ $theme }) => {
      const theme = $theme as CustomTheme;
      return {
        marginTop: '-6px',
        marginLeft: '0px',
        boxShadow: theme.colors['shadow.popup'],
        border: `1px solid ${theme.colors.divider}`,
      };
    },
  },
  Inner: {
    style: ({ $theme }) => {
      const theme = $theme as CustomTheme;
      return {
        padding: '12px 16px',
        backgroundColor: theme.colors['background.primary'],
      };
    },
  },
};
