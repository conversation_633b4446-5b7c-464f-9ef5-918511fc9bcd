import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import { Input } from '@tigergraph/app-ui-lib/input';
import searchIcon from '@/assets/search.svg';
import { TreeNode, TreeView } from 'baseui/tree-view';
import { FileStore, FileSortType, ActiveFile } from '@/utils/graphEditor/data';
import { ReactNode, useEffect, useMemo, useRef, useState } from 'react';
import { FileMenuButtonGroup } from './FileMenuButtonGroup';
import FileListLoading from './FileListLoading';
import { sortFile } from '@/utils/graphEditor';

interface BaseFileListProps {
  files: FileStore[];
  activeFiles: ActiveFile[];
  folderExpanded: Record<string, boolean>;
  currentFileId: string | null;
  isFetching?: boolean;
  type: 'query' | 'file';
  onToggleExpand: (id: string) => void;
  renderTreeNode: (
    file: FileStore,
    folder: FileStore | null,
    { onFileMenuOpen }: { onFileMenuOpen: (isOpen: boolean) => void }
  ) => ReactNode;
  onCreateFile: (file: { is_folder: boolean; content?: string; parentId?: string; name?: string }) => void;
  onRefresh: () => void;
}

const preventDefault = (e: Event) => e.preventDefault();

export function BaseFileList({
  files,
  activeFiles,
  folderExpanded,
  currentFileId,
  type,
  isFetching,
  renderTreeNode,
  onCreateFile,
  onRefresh,
  onToggleExpand,
}: BaseFileListProps) {
  const [css, theme] = useStyletron();
  const [sortType, setSortType] = useState<FileSortType>(
    type === 'file' ? FileSortType.DateCreated : FileSortType.Name
  );
  const [searchText, setSearchText] = useState<string>('');
  const fileListContainerRef = useRef<HTMLDivElement>(null);
  const [canScrollFileList, setCanScrollFileList] = useState(true);

  const currentSelectedId = activeFiles?.find((f) => f.id === currentFileId)?.file_id || currentFileId;

  const handleSortFile = (type: FileSortType) => {
    setSortType((prev) => (type === prev ? type : type));
  };

  const handleSearchTextChange = (val: string) => {
    setSearchText(val);
  };

  useEffect(() => {
    const ref = fileListContainerRef.current;
    if (!canScrollFileList) {
      ref?.addEventListener?.('wheel', preventDefault);
      ref?.addEventListener?.('touchmove', preventDefault);
    } else {
      ref?.removeEventListener?.('wheel', preventDefault);
      ref?.removeEventListener?.('touchmove', preventDefault);
    }
    return () => {
      ref?.removeEventListener?.('wheel', preventDefault);
      ref?.removeEventListener?.('touchmove', preventDefault);
    };
  }, [canScrollFileList]);

  const onFileMenuOpen = (isOpen: boolean) => {
    setCanScrollFileList(!isOpen);
  };

  const treeNodes = useMemo(() => {
    const genTreeNode = (file: FileStore, folder: FileStore | null): TreeNode | null => {
      const getNodeLabel = () => {
        if (file.type === 'Placeholder') {
          return <FileListLoading />;
        }

        return renderTreeNode(file, folder, { onFileMenuOpen });
      };

      const getTreeNode = (childNodes: TreeNode[]): TreeNode => {
        return {
          id: file.id,
          label: getNodeLabel,
          isExpanded: file.is_folder && folderExpanded[file.id],
          isSelected: currentFileId === file.id,
          is_folder: file.is_folder,
          children: childNodes,
        };
      };

      if (file.is_folder) {
        let childFiles = sortFile(file.files || [], sortType);
        let childNodes = childFiles.map((f) => genTreeNode(f, file)).filter((node) => node !== null) as TreeNode[];
        if (file.name.toLowerCase().includes(searchText.toLowerCase()) || childNodes.length > 0) {
          return getTreeNode(childNodes);
        }
      } else if (file.name.toLowerCase().includes(searchText.toLowerCase())) {
        return getTreeNode([]);
      }

      return null;
    };

    const sortedFiles = sortFile(files, sortType);
    return sortedFiles.map((f) => genTreeNode(f, null)).filter((node) => !!node) as TreeNode[];
  }, [files, sortType, searchText, renderTreeNode, folderExpanded, currentFileId]);

  const treeView = useMemo(
    () => (
      <TreeView
        overrides={{
          Root: {
            style: {
              flex: '1',
            },
          },
          TreeLabel: {
            style: {
              fontWeight: 'inherit',
              fontSize: '12px',
              color: 'inherit',
              ':hover': {
                backgroundColor: 'transparent',
              },
            },
          },
          TreeItem: {
            style: (props: any) => {
              const selected = currentSelectedId === (props['data-nodeid'] as string);
              const expanded = props['aria-expanded'];
              return {
                borderRadius: '4px',
                marginBottom: '4px',
                overflow: 'hidden',
                backgroundColor: selected ? theme.colors['list.background.selected'] : 'transparent',
                fontWeight: selected ? '700' : '400',
                ':hover': {
                  backgroundColor: selected
                    ? theme.colors['list.background.selected']
                    : !expanded
                    ? theme.colors['list.background.hover']
                    : 'transparent',
                },
              };
            },
          },
          ExpandIcon: {
            style: () => ({
              fontSize: '16px',
              color: theme.colors['icon.primary'],
            }),
          },
          CollapseIcon: {
            style: () => ({
              fontSize: '16px',
              color: theme.colors['icon.primary'],
            }),
          },
        }}
        onToggle={(node) => {
          onToggleExpand(`${node.id}`);
        }}
        data={treeNodes}
      />
    ),
    [treeNodes, currentSelectedId, theme.colors, onToggleExpand]
  );

  return (
    <div
      className={css({
        height: `100%`,
        width: '100%',
        flexShrink: 0,
        display: 'flex',
        flexDirection: 'column',
        position: 'relative',
        overflow: 'hidden',
      })}
    >
      <div
        className={css({
          borderRight: `1px solid ${theme.colors.divider}`,
          height: '100%',
        })}
      >
        <div
          className={css({
            padding: '8px',
            display: 'flex',
            alignItems: 'center',
          })}
        >
          <Input
            overrides={{
              Root: {
                style: {
                  borderTopWidth: '1px',
                  borderRightWidth: '1px',
                  borderBottomWidth: '1px',
                  borderLeftWidth: '1px',
                },
              },
              Input: {
                style: {
                  height: '32px',
                  paddingLeft: '8px',
                },
              },
            }}
            onChange={(e) => handleSearchTextChange(e.currentTarget.value)}
            onKeyDown={(e) => {
              e.stopPropagation();
            }}
            placeholder="Search..."
            startEnhancer={() => <img src={searchIcon} />}
          />
          <FileMenuButtonGroup
            sortType={sortType}
            onCreateFile={onCreateFile}
            onSort={handleSortFile}
            onRefresh={onRefresh}
            type={type}
          />
        </div>
        <div
          ref={fileListContainerRef}
          className={css({
            height: 'calc(100% - 57px)',
            display: 'flex',
            flexDirection: 'column',
            overflowY: 'auto',
            overflowX: 'hidden',
            '::-webkit-scrollbar': {
              width: '5px',
              backgroundColor: '#d4dadf',
              borderRadius: '20px',
            },

            '::-webkit-scrollbar-thumb': {
              backgroundColor: '#aab5bf',
              borderRadius: '20px',
            },
          })}
        >
          {isFetching ? (
            <div
              className={css({
                paddingLeft: '16px',
              })}
            >
              <FileListLoading />
            </div>
          ) : treeNodes.length > 0 ? (
            treeView
          ) : (
            <div className={css({ paddingLeft: '16px', ...theme.typography.Label })}>
              No {type === 'file' ? 'files' : 'queries'} found
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
