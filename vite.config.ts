/// <reference types="vitest" />

import react from '@vitejs/plugin-react-swc';
import { defineConfig, splitVendorChunkPlugin } from 'vite';
import tsconfigPaths from 'vite-tsconfig-paths';
import svgr from 'vite-plugin-svgr';
import dynamicImport from 'vite-plugin-dynamic-import';
import { resolve } from 'path';

// https://vitejs.dev/config/
export default defineConfig({
  base: '/',
  server: {
    host: 'localhost',
    port: 3000,
  },
  plugins: [svgr(), react(), tsconfigPaths(), splitVendorChunkPlugin(), dynamicImport()],
  build: {
    sourcemap: true,
    target: 'es2015',
    assetsInlineLimit: (filepath: string, content: Buffer) => {
      if (/lucide-static\/icons/.test(filepath) && filepath.endsWith('.svg')) {
        return false; // Do not inline Lucide icons
      }
      if (content.length < 1024) {
        return true; // Inline assets smaller than 1KB
      }
      return false;
    },
    rollupOptions: {
      input: {
        main: resolve(__dirname, 'index.html'),
        maintenance: resolve(__dirname, 'maintenance.html'),
        price: resolve(__dirname, 'price.html'),
      },
      output: {
        assetFileNames: (chunkInfo) => {
          if (chunkInfo.originalFileNames?.find((i) => /lucide-static\/icons/.test(i))) {
            return 'studio/assets/img/[name][extname]';
          }
          return 'assets/[name]-[hash][extname]';
        },
      },
    },
    commonjsOptions: {
      transformMixedEsModules: true,
    },
  },
  test: {
    globals: true,
    environment: 'jsdom',
    setupFiles: './src/setupTests.ts',
    exclude: ['**/assets/**', 'node_modules', 'bdd'],
    coverage: {
      provider: 'custom',
      customProviderModule: 'vitest-monocart-coverage',
    },
  },
});
